#!/usr/bin/env python
# -*- coding: utf-8 -*-

import qlib
import pandas as pd
import numpy as np

def check_stock_data(instrument, start_time='2025-05-20', end_time='2025-05-22'):
    """检查股票数据"""
    print(f'\n=== {instrument} 价格数据分析 ===')
    
    try:
        # 获取数据
        data = qlib.data.D.features(
            instruments=[instrument],
            fields=['$close', '$open', '$high', '$low', '$volume'],
            start_time=start_time,
            end_time=end_time,
            freq='5min'
        )
        
        if data is None or data.empty:
            print(f"{instrument}: 无数据")
            return
            
        print(f"数据形状: {data.shape}")
        print(f"时间范围: {data.index.get_level_values('datetime').min()} 到 {data.index.get_level_values('datetime').max()}")
        
        # 价格统计
        close_prices = data['$close']
        print(f"收盘价范围: {close_prices.min():.6f} - {close_prices.max():.6f}")
        print(f"收盘价均值: {close_prices.mean():.6f}")
        print(f"收盘价标准差: {close_prices.std():.6f}")
        
        # 显示最近几条数据
        print("最近10条数据:")
        print(data.head(10))
        
        # 检查是否有异常值
        print(f"是否有NaN值: {data.isnull().any().any()}")
        print(f"是否有负值: {(data < 0).any().any()}")
        print(f"是否有零值: {(data == 0).any().any()}")
        
        # 检查价格合理性（一般股票价格应该大于1）
        if close_prices.max() < 2:
            print("⚠️  警告: 价格过低，可能是归一化后的数据或有问题的数据")
        
        return data
        
    except Exception as e:
        print(f"获取{instrument}数据时出错: {e}")
        return None

def main():
    # 初始化qlib
    qlib.init(provider_uri='./qlib_data', region='cn')
    
    # 检查两只股票的数据
    data_513980 = check_stock_data('513980')
    data_513120 = check_stock_data('513120')
    
    # 尝试获取更长时间范围的数据看看
    print(f'\n=== 检查更长时间范围的数据 ===')
    try:
        data_long = qlib.data.D.features(
            instruments=['513980', '513120'],
            fields=['$close'],
            start_time='2025-01-01',
            end_time='2025-05-22',
            freq='5min'
        )
        
        if data_long is not None and not data_long.empty:
            print(f"长期数据形状: {data_long.shape}")
            
            for instrument in ['513980', '513120']:
                instrument_data = data_long.xs(instrument, level='instrument')
                if not instrument_data.empty:
                    close_prices = instrument_data['$close']
                    print(f"{instrument} 长期价格范围: {close_prices.min():.6f} - {close_prices.max():.6f}")
                    
                    # 检查是否价格一直都很低
                    if close_prices.max() < 2:
                        print(f"⚠️  {instrument}: 长期数据价格都很低，可能是数据问题")
                        
    except Exception as e:
        print(f"获取长期数据时出错: {e}")
    
    # 检查instruments文件
    print(f'\n=== 检查instruments配置 ===')
    try:
        import os
        instruments_dir = './qlib_data/instruments'
        if os.path.exists(instruments_dir):
            for file in os.listdir(instruments_dir):
                if file.endswith('.txt'):
                    filepath = os.path.join(instruments_dir, file)
                    with open(filepath, 'r') as f:
                        content = f.read().strip()
                        print(f"{file}: {content}")
    except Exception as e:
        print(f"检查instruments文件时出错: {e}")

if __name__ == "__main__":
    main() 