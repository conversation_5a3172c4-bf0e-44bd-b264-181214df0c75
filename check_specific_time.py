#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import qlib
from qlib.data import D
from qlib.config import REG_CN

def check_specific_time():
    """
    检查特定时间点的数据
    """
    # 初始化qlib
    qlib_dir = "./qlib_data"
    qlib.init(provider_uri=qlib_dir, region=REG_CN)

    # 检查2025-05-22 10:45:00这个时间点
    target_time = "2025-05-22 10:45:00"

    # 从qlib中获取数据
    fields = ['$open', '$high', '$low', '$close', '$volume', '$amount']

    # 获取更大的时间范围
    start_time = "2025-05-22 10:40:00"
    end_time = "2025-05-22 10:50:00"

    try:
        # 检查513980和513120的数据
        for symbol in ["513980", "513120"]:
            print(f"\n=== 检查股票 {symbol} ===")
            qlib_df = D.features([symbol], fields, start_time=start_time, end_time=end_time, freq='5min')

            print(f"qlib数据 ({start_time} 到 {end_time}):")
            print(qlib_df)

            # 检查是否有目标时间点
            if len(qlib_df) > 0:
                qlib_df_reset = qlib_df.reset_index()
                target_row = qlib_df_reset[qlib_df_reset['datetime'] == pd.Timestamp(target_time)]

                if len(target_row) > 0:
                    print(f"\n目标时间点 {target_time} 的数据:")
                    print(target_row)

                    # 获取价格数据
                    open_price = target_row['$open'].iloc[0]
                    high_price = target_row['$high'].iloc[0]
                    low_price = target_row['$low'].iloc[0]
                    close_price = target_row['$close'].iloc[0]

                    print(f"\n价格数据:")
                    print(f"开盘价: {open_price}")
                    print(f"最高价: {high_price}")
                    print(f"最低价: {low_price}")
                    print(f"收盘价: {close_price}")

                    # 检查是否接近0.735
                    if abs(close_price - 0.735) < 0.001:
                        print(f"警告：收盘价 {close_price} 接近 0.735，可能存在数据错误！")
                    elif abs(close_price - 0.721) < 0.001:
                        print(f"正常：收盘价 {close_price} 符合预期的 0.721")
                    else:
                        print(f"异常：收盘价 {close_price} 既不是 0.735 也不是 0.721")
                else:
                    print(f"\n在qlib数据中找不到时间点 {target_time}")
            else:
                print(f"\n在指定时间范围内没有找到任何数据")

            # 检查是否有价格为0.735的数据点
            if len(qlib_df) > 0:
                qlib_df_reset = qlib_df.reset_index()
                price_735_rows = qlib_df_reset[abs(qlib_df_reset['$close'] - 0.735) < 0.001]

                if len(price_735_rows) > 0:
                    print(f"\n发现价格接近0.735的数据点:")
                    print(price_735_rows[['datetime', '$open', '$high', '$low', '$close']])
                else:
                    print(f"\n在指定时间范围内没有发现价格接近0.735的数据点")

    except Exception as e:
        print(f"从qlib获取数据时出错: {e}")

if __name__ == "__main__":
    check_specific_time()
