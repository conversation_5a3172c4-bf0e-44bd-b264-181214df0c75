#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

def compare_data(symbol):
    """
    比较原始CSV数据和处理后的数据
    
    Args:
        symbol: 股票代码
    """
    # 读取原始数据
    original_file = f"kline_{symbol}.sh_origin.csv"
    if not os.path.exists(original_file):
        print(f"原始文件不存在: {original_file}")
        return
    
    original_df = pd.read_csv(original_file)
    
    # 读取处理后的数据
    processed_file = f"prepared_{symbol}.csv"
    if not os.path.exists(processed_file):
        print(f"处理后的文件不存在: {processed_file}")
        return
    
    processed_df = pd.read_csv(processed_file)
    
    # 确保日期格式一致
    original_df['datetime'] = pd.to_datetime(original_df['datetime'], format='%Y%m%d%H%M%S')
    processed_df['date'] = pd.to_datetime(processed_df['date'])
    
    # 设置日期为索引以便合并
    original_df.set_index('datetime', inplace=True)
    processed_df.set_index('date', inplace=True)
    
    # 合并数据
    merged_df = pd.merge(
        original_df[['openPrice', 'highPrice', 'lowPrice', 'closePrice']], 
        processed_df[['open', 'high', 'low', 'close']],
        left_index=True, right_index=True,
        how='inner'
    )
    
    # 计算差异
    merged_df['open_diff'] = merged_df['openPrice'] - merged_df['open']
    merged_df['high_diff'] = merged_df['highPrice'] - merged_df['high']
    merged_df['low_diff'] = merged_df['lowPrice'] - merged_df['low']
    merged_df['close_diff'] = merged_df['closePrice'] - merged_df['close']
    
    # 输出统计信息
    print(f"\n股票 {symbol} 数据比较:")
    print(f"合并后的数据行数: {len(merged_df)}")
    
    # 检查是否有差异
    has_diff = False
    for col in ['open_diff', 'high_diff', 'low_diff', 'close_diff']:
        if not np.allclose(merged_df[col], 0, atol=1e-10):
            has_diff = True
            non_zero_diff = merged_df[merged_df[col] != 0]
            print(f"\n{col} 存在差异的行数: {len(non_zero_diff)}")
            if len(non_zero_diff) > 0:
                print(f"差异统计: 最小值={non_zero_diff[col].min()}, 最大值={non_zero_diff[col].max()}, 平均值={non_zero_diff[col].mean()}")
                print("\n差异示例:")
                print(non_zero_diff[[col.replace('_diff', 'Price'), col.replace('_diff', ''), col]].head())
    
    if not has_diff:
        print("\n所有价格数据完全匹配!")
    
    # 返回合并后的数据框
    return merged_df

if __name__ == "__main__":
    # 比较两个股票的数据
    for symbol in ["513980", "513120"]:
        merged_df = compare_data(symbol)
