#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os
import sys
import logging
import subprocess
from pathlib import Path
import shutil
import numpy as np
import qlib
from qlib.data import D
from qlib.config import REG_CN

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def prepare_data(input_file, output_file, symbol):
    """
    准备数据，将原始CSV转换为qlib格式

    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
        symbol: 股票代码
    """
    logger.info(f"读取文件: {input_file}")

    # 读取CSV文件
    df = pd.read_csv(input_file)

    logger.info(f"数据行数: {len(df)}")

    # 重命名列以匹配qlib格式
    column_mapping = {
        'datetime': 'date',
        'openPrice': 'open',
        'highPrice': 'high',
        'lowPrice': 'low',
        'closePrice': 'close',
        'tradeVolume': 'volume',
        'amount': 'amount'
    }

    df = df.rename(columns=column_mapping)

    # 转换日期格式
    df['date'] = pd.to_datetime(df['date'], format='%Y%m%d%H%M%S')

    # 添加股票代码列
    df['symbol'] = symbol

    # 重新排列列的顺序，只包含存在的列
    available_columns = ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume', 'amount']
    optional_columns = ['averagePrice', 'iopv']

    # 添加存在的可选列
    for col in optional_columns:
        if col in df.columns:
            available_columns.append(col)

    df = df[available_columns]

    logger.info(f"数据日期范围: {df['date'].min()} 至 {df['date'].max()}")

    # 保存处理后的数据
    logger.info(f"保存处理后的数据到: {output_file}")
    df.to_csv(output_file, index=False)

    return df

def create_single_stock_qlib(csv_file, qlib_dir, symbol, freq="5min"):
    """
    为单个股票创建独立的qlib数据库

    Args:
        csv_file: CSV文件路径
        qlib_dir: qlib数据目录
        symbol: 股票代码
        freq: 数据频率
    """
    logger.info(f"为股票 {symbol} 创建独立的qlib数据库")

    # 确保qlib目录存在
    os.makedirs(qlib_dir, exist_ok=True)

    # 使用dump_bin.py脚本导入数据
    cmd = [
        "python", "qlib/scripts/dump_bin.py", "dump_all",
        f"--csv_path={csv_file}",
        f"--qlib_dir={qlib_dir}",
        f"--freq={freq}",
        "--date_field_name=date",
        "--symbol_field_name=symbol",
        "--exclude_fields=symbol"
    ]

    logger.info(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("数据导入成功")
        logger.info(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"数据导入失败: {e}")
        logger.error(e.stdout)
        logger.error(e.stderr)
        return False

def verify_qlib_data(qlib_dir, symbol, original_csv):
    """
    验证qlib中的数据与原始数据是否一致

    Args:
        qlib_dir: qlib数据目录
        symbol: 股票代码
        original_csv: 原始CSV文件路径
    """
    logger.info(f"验证qlib数据与原始数据是否一致: {symbol}")

    # 初始化qlib
    qlib.init(provider_uri=qlib_dir, region=REG_CN)

    # 读取原始数据
    original_df = pd.read_csv(original_csv)
    original_df['datetime'] = pd.to_datetime(original_df['datetime'], format='%Y%m%d%H%M%S')

    # 重命名列以便比较
    column_mapping = {
        'datetime': 'date',
        'openPrice': 'open',
        'highPrice': 'high',
        'lowPrice': 'low',
        'closePrice': 'close',
        'tradeVolume': 'volume',
        'amount': 'amount'
    }
    original_df = original_df.rename(columns=column_mapping)

    # 从qlib中获取数据
    start_date = original_df['date'].min().strftime('%Y-%m-%d')
    end_date = original_df['date'].max().strftime('%Y-%m-%d')

    fields = ['$open', '$high', '$low', '$close', '$volume', '$amount']
    try:
        qlib_df = D.features([symbol], fields, start_time=start_date, end_time=end_date, freq='5min')

        # 重命名列以便比较
        qlib_df.columns = [c.replace('$', '') for c in qlib_df.columns]

        # 重置qlib数据的索引以便合并
        qlib_df = qlib_df.reset_index()
        qlib_df = qlib_df.rename(columns={'datetime': 'date'})

        # 合并数据进行比较
        merged_df = pd.merge(
            qlib_df,
            original_df[['date', 'open', 'high', 'low', 'close', 'volume', 'amount']],
            on='date',
            how='inner',
            suffixes=('_qlib', '_orig')
        )

        logger.info(f"qlib数据行数: {len(qlib_df)}")
        logger.info(f"原始数据行数: {len(original_df)}")
        logger.info(f"合并后的数据行数: {len(merged_df)}")

        # 检查特定时间点的数据
        target_time = "2025-05-22 10:45:00"
        target_row = merged_df[merged_df['date'] == pd.Timestamp(target_time)]

        if len(target_row) > 0:
            qlib_close = target_row['close_qlib'].iloc[0]
            orig_close = target_row['close_orig'].iloc[0]

            logger.info(f"时间点 {target_time}:")
            logger.info(f"qlib收盘价: {qlib_close}")
            logger.info(f"原始收盘价: {orig_close}")
            logger.info(f"差异: {qlib_close - orig_close}")

            if abs(qlib_close - orig_close) < 0.001:
                logger.info("✅ 数据匹配正确!")
                return True
            else:
                logger.error("❌ 数据不匹配!")
                return False
        else:
            logger.warning(f"未找到时间点 {target_time} 的数据")
            return False

    except Exception as e:
        logger.error(f"从qlib获取数据时出错: {e}")
        return False

def process_single_stock(symbol, input_file, qlib_base_dir):
    """
    处理单个股票，创建独立的qlib数据库

    Args:
        symbol: 股票代码
        input_file: 输入CSV文件
        qlib_base_dir: qlib基础目录
    """
    logger.info(f"开始处理股票: {symbol}")

    # 为每个股票创建独立的目录
    stock_qlib_dir = os.path.join(qlib_base_dir, f"qlib_data_{symbol}")
    prepared_file = f"prepared_{symbol}.csv"

    # 清除现有目录
    if os.path.exists(stock_qlib_dir):
        shutil.rmtree(stock_qlib_dir)

    # 准备数据
    prepare_data(input_file, prepared_file, symbol)

    # 创建qlib数据库
    success = create_single_stock_qlib(prepared_file, stock_qlib_dir, symbol)

    if success:
        # 验证数据
        verify_success = verify_qlib_data(stock_qlib_dir, symbol, input_file)
        if verify_success:
            logger.info(f"✅ 股票 {symbol} 处理成功")
        else:
            logger.error(f"❌ 股票 {symbol} 数据验证失败")
    else:
        logger.error(f"❌ 股票 {symbol} 处理失败")

    return success

if __name__ == "__main__":
    qlib_base_dir = "./qlib_data_safe"

    # 定义股票配置
    stock_configs = {
        "513980": "kline_513980.sh_origin.csv",
        "513120": "kline_513120.sh_origin.csv"
    }

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] in stock_configs:
        symbols_to_process = [sys.argv[1]]
    else:
        symbols_to_process = list(stock_configs.keys())

    for symbol in symbols_to_process:
        input_file = stock_configs[symbol]
        process_single_stock(symbol, input_file, qlib_base_dir)

    logger.info("所有股票处理完成")
