#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os
import sys
import logging
import subprocess
from pathlib import Path
import shutil
import numpy as np
import qlib
from qlib.data import D
from qlib.config import REG_CN

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def prepare_data(input_file, output_file, symbol):
    """
    准备数据，将原始CSV转换为qlib格式

    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
        symbol: 股票代码
    """
    logger.info(f"读取文件: {input_file}")

    # 读取CSV文件
    df = pd.read_csv(input_file)

    logger.info(f"数据行数: {len(df)}")

    # 重命名列以匹配qlib格式
    column_mapping = {
        'datetime': 'date',
        'openPrice': 'open',
        'highPrice': 'high',
        'lowPrice': 'low',
        'closePrice': 'close',
        'tradeVolume': 'volume',
        'amount': 'amount'
    }

    df = df.rename(columns=column_mapping)

    # 转换日期格式
    df['date'] = pd.to_datetime(df['date'], format='%Y%m%d%H%M%S')

    # 添加股票代码列
    df['symbol'] = symbol

    # 重新排列列的顺序，只包含基本列
    basic_columns = ['symbol', 'date', 'open', 'high', 'low', 'close', 'volume', 'amount']
    optional_columns = ['averagePrice', 'iopv']

    # 添加存在的可选列
    available_columns = basic_columns.copy()
    for col in optional_columns:
        if col in df.columns:
            available_columns.append(col)

    df = df[available_columns]

    logger.info(f"数据日期范围: {df['date'].min()} 至 {df['date'].max()}")

    # 保存处理后的数据
    logger.info(f"保存处理后的数据到: {output_file}")
    df.to_csv(output_file, index=False)

    return df

def create_stock_csv_files(prepared_files, csv_dir):
    """
    为每个股票创建单独的CSV文件，文件名为股票代码

    Args:
        prepared_files: 准备好的CSV文件列表
        csv_dir: CSV文件目录
    """
    logger.info(f"为每个股票创建单独的CSV文件到目录: {csv_dir}")

    # 确保目录存在
    os.makedirs(csv_dir, exist_ok=True)

    stock_files = []
    for file in prepared_files:
        if os.path.exists(file):
            df = pd.read_csv(file)
            df['date'] = pd.to_datetime(df['date'])

            # 获取股票代码
            symbol = df['symbol'].iloc[0]

            # 移除symbol列（qlib不需要）
            df_no_symbol = df.drop('symbol', axis=1)

            # 创建以股票代码命名的文件
            stock_file = os.path.join(csv_dir, f"{symbol}.csv")
            df_no_symbol.to_csv(stock_file, index=False)
            stock_files.append(stock_file)

            logger.info(f"创建股票文件 {stock_file}: {len(df)} 行")

    return stock_files

def dump_to_qlib_once(csv_dir, qlib_dir, freq="5min"):
    """
    一次性将CSV目录中的数据导入到qlib中

    Args:
        csv_dir: CSV文件目录
        qlib_dir: qlib数据目录
        freq: 数据频率
    """
    logger.info(f"一次性导入数据到qlib: {csv_dir} -> {qlib_dir}")

    # 确保qlib目录不存在（完全重新创建）
    if os.path.exists(qlib_dir):
        logger.info(f"删除现有qlib目录: {qlib_dir}")
        shutil.rmtree(qlib_dir)

    # 使用dump_bin.py脚本导入数据
    cmd = [
        "python", "qlib/scripts/dump_bin.py", "dump_all",
        f"--csv_path={csv_dir}",
        f"--qlib_dir={qlib_dir}",
        f"--freq={freq}",
        "--date_field_name=date",
        "--symbol_field_name=symbol"
    ]

    logger.info(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("数据导入成功")
        logger.info(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"数据导入失败: {e}")
        logger.error(e.stdout)
        logger.error(e.stderr)
        return False

def verify_qlib_data(qlib_dir, symbol, original_csv):
    """
    验证qlib中的数据与原始数据是否一致

    Args:
        qlib_dir: qlib数据目录
        symbol: 股票代码
        original_csv: 原始CSV文件路径
    """
    logger.info(f"验证qlib数据与原始数据是否一致: {symbol}")

    # 初始化qlib
    qlib.init(provider_uri=qlib_dir, region=REG_CN)

    # 读取原始数据
    original_df = pd.read_csv(original_csv)
    original_df['datetime'] = pd.to_datetime(original_df['datetime'], format='%Y%m%d%H%M%S')

    # 重命名列以便比较
    column_mapping = {
        'datetime': 'date',
        'openPrice': 'open',
        'highPrice': 'high',
        'lowPrice': 'low',
        'closePrice': 'close',
        'tradeVolume': 'volume',
        'amount': 'amount'
    }
    original_df = original_df.rename(columns=column_mapping)

    # 从qlib中获取数据
    start_date = original_df['date'].min().strftime('%Y-%m-%d')
    end_date = original_df['date'].max().strftime('%Y-%m-%d')

    fields = ['$open', '$high', '$low', '$close', '$volume', '$amount']
    try:
        qlib_df = D.features([symbol], fields, start_time=start_date, end_time=end_date, freq='5min')

        # 重命名列以便比较
        qlib_df.columns = [c.replace('$', '') for c in qlib_df.columns]

        # 重置qlib数据的索引以便合并
        qlib_df = qlib_df.reset_index()
        qlib_df = qlib_df.rename(columns={'datetime': 'date'})

        # 合并数据进行比较
        merged_df = pd.merge(
            qlib_df,
            original_df[['date', 'open', 'high', 'low', 'close', 'volume', 'amount']],
            on='date',
            how='inner',
            suffixes=('_qlib', '_orig')
        )

        logger.info(f"qlib数据行数: {len(qlib_df)}")
        logger.info(f"原始数据行数: {len(original_df)}")
        logger.info(f"合并后的数据行数: {len(merged_df)}")

        # 检查特定时间点的数据
        target_time = "2025-05-22 10:45:00"
        target_row = merged_df[merged_df['date'] == pd.Timestamp(target_time)]

        if len(target_row) > 0:
            qlib_close = target_row['close_qlib'].iloc[0]
            orig_close = target_row['close_orig'].iloc[0]

            logger.info(f"时间点 {target_time}:")
            logger.info(f"qlib收盘价: {qlib_close}")
            logger.info(f"原始收盘价: {orig_close}")
            logger.info(f"差异: {qlib_close - orig_close}")

            if abs(qlib_close - orig_close) < 0.001:
                logger.info("✅ 数据匹配正确!")
                return True
            else:
                logger.error("❌ 数据不匹配!")
                return False
        else:
            logger.warning(f"未找到时间点 {target_time} 的数据")
            # 检查是否有任何数据匹配
            if len(merged_df) > 0:
                # 计算整体差异
                close_diff = merged_df['close_qlib'] - merged_df['close_orig']
                avg_diff = close_diff.mean()
                max_diff = close_diff.abs().max()

                logger.info(f"整体数据差异: 平均={avg_diff:.6f}, 最大={max_diff:.6f}")

                if max_diff < 0.001:
                    logger.info("✅ 整体数据匹配正确!")
                    return True
                else:
                    logger.error("❌ 整体数据存在较大差异!")
                    return False
            else:
                logger.error("❌ 没有匹配的数据!")
                return False

    except Exception as e:
        logger.error(f"从qlib获取数据时出错: {e}")
        return False

if __name__ == "__main__":
    qlib_dir = "./qlib_data"
    freq = "5min"

    # 定义股票配置
    stock_configs = {
        "513980": "kline_513980.sh_origin.csv",
        "513120": "kline_513120.sh_origin.csv"
    }

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] in stock_configs:
        symbols_to_process = [sys.argv[1]]
    else:
        symbols_to_process = list(stock_configs.keys())

    logger.info(f"处理股票: {symbols_to_process}")

    # 第一步：准备所有股票的数据
    prepared_files = []
    for symbol in symbols_to_process:
        input_file = stock_configs[symbol]
        output_file = f"prepared_{symbol}.csv"

        logger.info(f"准备股票数据: {symbol}")
        prepare_data(input_file, output_file, symbol)
        prepared_files.append(output_file)

    # 第二步：为每个股票创建单独的CSV文件
    csv_dir = "stock_csv_files"
    stock_files = create_stock_csv_files(prepared_files, csv_dir)

    if stock_files:
        # 第三步：一次性导入到qlib
        success = dump_to_qlib_once(csv_dir, qlib_dir, freq)

        if success:
            # 第四步：验证每个股票的数据
            all_verified = True
            for symbol in symbols_to_process:
                input_file = stock_configs[symbol]
                verified = verify_qlib_data(qlib_dir, symbol, input_file)
                if not verified:
                    all_verified = False
                logger.info(f"股票 {symbol} 验证结果: {'✅ 成功' if verified else '❌ 失败'}")

            if all_verified:
                logger.info("🎉 所有股票数据处理和验证成功!")
            else:
                logger.error("❌ 部分股票数据验证失败!")
        else:
            logger.error("❌ qlib数据导入失败!")
    else:
        logger.error("❌ CSV文件合并失败!")

    logger.info("处理完成")
