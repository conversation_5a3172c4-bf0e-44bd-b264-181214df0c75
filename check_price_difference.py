#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import os
import sys
import qlib
from qlib.data import D
from qlib.config import REG_CN
import matplotlib.pyplot as plt

def check_price_difference(symbol):
    """
    检查原始数据和qlib数据之间的价格差异
    
    Args:
        symbol: 股票代码
    """
    print(f"\n===== 检查股票 {symbol} 的价格差异 =====")
    
    # 初始化qlib
    qlib_dir = "./qlib_data"
    qlib.init(provider_uri=qlib_dir, region=REG_CN)
    
    # 读取原始CSV数据
    original_file = f"kline_{symbol}.sh_origin.csv"
    if not os.path.exists(original_file):
        print(f"原始文件不存在: {original_file}")
        return
    
    original_df = pd.read_csv(original_file)
    original_df['datetime'] = pd.to_datetime(original_df['datetime'], format='%Y%m%d%H%M%S')
    
    # 获取日期范围
    start_date = original_df['datetime'].min().strftime('%Y-%m-%d')
    end_date = original_df['datetime'].max().strftime('%Y-%m-%d')
    
    print(f"原始数据日期范围: {start_date} 至 {end_date}")
    print(f"原始数据行数: {len(original_df)}")
    
    # 从qlib中获取数据
    try:
        fields = ['$open', '$high', '$low', '$close', '$volume', '$amount']
        qlib_df = D.features([symbol], fields, start_time=start_date, end_time=end_date, freq='5min')
        
        print(f"qlib数据行数: {len(qlib_df)}")
        
        # 重命名列以便比较
        qlib_df.columns = [c.replace('$', '') for c in qlib_df.columns]
        
        # 重置索引以便合并
        qlib_df = qlib_df.reset_index()
        qlib_df = qlib_df.rename(columns={'datetime': 'date'})
        
        # 重命名原始数据列以便比较
        column_mapping = {
            'datetime': 'date',
            'openPrice': 'open',
            'highPrice': 'high',
            'lowPrice': 'low',
            'closePrice': 'close',
            'tradeVolume': 'volume',
            'amount': 'amount'
        }
        original_df = original_df.rename(columns=column_mapping)
        
        # 合并数据进行比较
        merged_df = pd.merge(
            qlib_df, 
            original_df[['date', 'open', 'high', 'low', 'close', 'volume', 'amount']],
            on='date',
            how='inner',
            suffixes=('_qlib', '_orig')
        )
        
        print(f"合并后的数据行数: {len(merged_df)}")
        
        # 计算差异
        for field in ['open', 'high', 'low', 'close']:
            merged_df[f'{field}_diff'] = merged_df[f'{field}_qlib'] - merged_df[f'{field}_orig']
            merged_df[f'{field}_ratio'] = merged_df[f'{field}_diff'] / merged_df[f'{field}_orig']
        
        # 检查是否有差异
        has_diff = False
        for field in ['open', 'high', 'low', 'close']:
            diff_col = f'{field}_diff'
            ratio_col = f'{field}_ratio'
            if not np.allclose(merged_df[diff_col], 0, atol=1e-10):
                has_diff = True
                non_zero_diff = merged_df[merged_df[diff_col] != 0]
                print(f"\n{diff_col} 存在差异的行数: {len(non_zero_diff)}")
                if len(non_zero_diff) > 0:
                    print(f"差异统计: 最小值={non_zero_diff[diff_col].min()}, 最大值={non_zero_diff[diff_col].max()}, 平均值={non_zero_diff[diff_col].mean()}")
                    print(f"比例统计: 最小值={non_zero_diff[ratio_col].min()}, 最大值={non_zero_diff[ratio_col].max()}, 平均值={non_zero_diff[ratio_col].mean()}")
                    print("\n差异示例:")
                    print(non_zero_diff[['date', f'{field}_qlib', f'{field}_orig', diff_col, ratio_col]].head())
        
        if not has_diff:
            print("\n所有价格数据完全匹配!")
        
        # 绘制价格差异图表
        plt.figure(figsize=(12, 8))
        
        # 绘制原始价格和qlib价格
        plt.subplot(2, 1, 1)
        plt.plot(merged_df['date'], merged_df['close_orig'], label='原始收盘价')
        plt.plot(merged_df['date'], merged_df['close_qlib'], label='qlib收盘价')
        plt.title(f'股票 {symbol} 价格对比')
        plt.legend()
        plt.grid(True)
        
        # 绘制价格差异
        plt.subplot(2, 1, 2)
        plt.plot(merged_df['date'], merged_df['close_diff'], label='收盘价差异')
        plt.title(f'股票 {symbol} 价格差异')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(f'price_diff_{symbol}.png')
        print(f"\n价格差异图表已保存为: price_diff_{symbol}.png")
        
        # 检查是否存在固定比例的差异
        if has_diff:
            # 计算每个时间点的比例
            ratios = merged_df['close_qlib'] / merged_df['close_orig']
            
            # 计算比例的统计信息
            ratio_mean = ratios.mean()
            ratio_std = ratios.std()
            
            print(f"\n价格比例统计:")
            print(f"平均比例: {ratio_mean}")
            print(f"比例标准差: {ratio_std}")
            
            # 如果标准差很小，说明存在固定比例的差异
            if ratio_std < 0.01:
                print(f"\n发现固定比例的差异，比例约为: {ratio_mean:.4f}")
                print(f"这可能是由于单位转换或者其他系统性因素导致的")
            
            # 检查是否存在固定偏移
            offsets = merged_df['close_qlib'] - merged_df['close_orig']
            offset_mean = offsets.mean()
            offset_std = offsets.std()
            
            print(f"\n价格偏移统计:")
            print(f"平均偏移: {offset_mean}")
            print(f"偏移标准差: {offset_std}")
            
            # 如果标准差很小，说明存在固定偏移的差异
            if offset_std < 0.01:
                print(f"\n发现固定偏移的差异，偏移量约为: {offset_mean:.4f}")
                print(f"这可能是由于基点差异或者其他系统性因素导致的")
        
        return merged_df
    
    except Exception as e:
        print(f"从qlib获取数据时出错: {e}")
        return None

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        symbols = [sys.argv[1]]
    else:
        symbols = ["513980", "513120"]
    
    # 比较每个股票的数据
    for symbol in symbols:
        check_price_difference(symbol)
