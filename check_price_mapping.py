#!/usr/bin/env python
# -*- coding: utf-8 -*-

import qlib
import pandas as pd
import numpy as np
import os

def check_price_mapping(instrument='513980'):
    """检查价格映射是否正确"""
    print(f'\n=== 检查 {instrument} 的价格映射 ===')
    
    # 初始化qlib
    qlib.init(provider_uri='./qlib_data', region='cn')
    
    # 获取预测时间范围的实际价格数据
    actual_data = qlib.data.D.features(
        instruments=[instrument],
        fields=['$close', '$open', '$high', '$low', '$volume'],
        start_time='2025-05-12',
        end_time='2025-05-22',
        freq='5min'
    )
    
    print(f"实际数据形状: {actual_data.shape}")
    print(f"实际数据时间范围: {actual_data.index.get_level_values('datetime').min()} 到 {actual_data.index.get_level_values('datetime').max()}")
    
    # 检查预测结果文件
    prediction_file = f'prediction_results/predictions_infer_{instrument}.csv'
    trading_file = f'prediction_results/trading_decisions_{instrument}.csv'
    
    if os.path.exists(prediction_file):
        print(f"\n--- 检查预测文件 {prediction_file} ---")
        pred_df = pd.read_csv(prediction_file, index_col=[0, 1])
        print(f"预测文件形状: {pred_df.shape}")
        print("预测文件前5行:")
        print(pred_df.head())
        
        # 获取预测文件中的时间范围
        pred_times = pred_df.index.get_level_values(0)
        print(f"预测文件时间范围: {pred_times.min()} 到 {pred_times.max()}")
        
    if os.path.exists(trading_file):
        print(f"\n--- 检查交易决策文件 {trading_file} ---")
        trading_df = pd.read_csv(trading_file, index_col=0)
        trading_df.index = pd.to_datetime(trading_df.index)
        print(f"交易决策文件形状: {trading_df.shape}")
        
        # 显示一些关键时间点的价格对比
        print("\n--- 关键时间点价格对比 ---")
        sample_times = trading_df.index[::50]  # 每50个点取一个样本
        
        for time in sample_times[:10]:  # 只显示前10个样本
            trading_price = trading_df.loc[time, 'price']
            
            # 获取对应时间的实际价格
            try:
                actual_price = actual_data.loc[(instrument, time), '$close']
                price_diff = abs(trading_price - actual_price)
                
                print(f"{time}: 交易价格={trading_price:.6f}, 实际价格={actual_price:.6f}, 差异={price_diff:.6f}")
                
                if price_diff > 0.001:  # 如果差异大于0.001
                    print(f"  ⚠️  价格差异较大!")
                    
            except KeyError:
                print(f"{time}: 交易价格={trading_price:.6f}, 实际数据中无此时间点")
        
        # 检查最后几个交易决策的价格
        print(f"\n--- 最后5个交易决策的价格检查 ---")
        last_5 = trading_df.tail(5)
        for time, row in last_5.iterrows():
            trading_price = row['price']
            try:
                actual_price = actual_data.loc[(instrument, time), '$close']
                price_diff = abs(trading_price - actual_price)
                
                print(f"{time}: 决策={row['decision']}, 交易价格={trading_price:.6f}, 实际价格={actual_price:.6f}, 差异={price_diff:.6f}")
                
            except KeyError:
                print(f"{time}: 决策={row['decision']}, 交易价格={trading_price:.6f}, 实际数据中无此时间点")
    
    # 检查实际数据的最后几个时间点
    print(f"\n--- 实际数据最后5个时间点 ---")
    last_actual = actual_data.tail(5)
    for (inst, time), row in last_actual.iterrows():
        print(f"{time}: close={row['$close']:.6f}, open={row['$open']:.6f}, high={row['$high']:.6f}, low={row['$low']:.6f}")

def main():
    # 检查两只股票
    check_price_mapping('513980')
    check_price_mapping('513120')

if __name__ == "__main__":
    main() 