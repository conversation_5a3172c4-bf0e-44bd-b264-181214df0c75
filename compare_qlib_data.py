#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import os
import sys
import qlib
from qlib.data import D
from qlib.config import REG_CN

def compare_qlib_data(symbol):
    """
    比较qlib中的数据和原始CSV数据
    
    Args:
        symbol: 股票代码
    """
    print(f"\n===== 比较股票 {symbol} 的数据 =====")
    
    # 初始化qlib
    qlib_dir = "./qlib_data"
    qlib.init(provider_uri=qlib_dir, region=REG_CN)
    
    # 读取原始CSV数据
    original_file = f"kline_{symbol}.sh_origin.csv"
    if not os.path.exists(original_file):
        print(f"原始文件不存在: {original_file}")
        return
    
    original_df = pd.read_csv(original_file)
    original_df['datetime'] = pd.to_datetime(original_df['datetime'], format='%Y%m%d%H%M%S')
    
    # 获取日期范围
    start_date = original_df['datetime'].min().strftime('%Y-%m-%d')
    end_date = original_df['datetime'].max().strftime('%Y-%m-%d')
    
    print(f"原始数据日期范围: {start_date} 至 {end_date}")
    print(f"原始数据行数: {len(original_df)}")
    
    # 从qlib中获取数据
    try:
        fields = ['$open', '$high', '$low', '$close', '$volume', '$amount']
        qlib_df = D.features([symbol], fields, start_time=start_date, end_time=end_date, freq='5min')
        
        print(f"qlib数据行数: {len(qlib_df)}")
        
        # 重命名列以便比较
        qlib_df.columns = [c.replace('$', '') for c in qlib_df.columns]
        
        # 重置索引以便合并
        qlib_df = qlib_df.reset_index()
        qlib_df = qlib_df.rename(columns={'datetime': 'date'})
        
        # 重命名原始数据列以便比较
        column_mapping = {
            'datetime': 'date',
            'openPrice': 'open',
            'highPrice': 'high',
            'lowPrice': 'low',
            'closePrice': 'close',
            'tradeVolume': 'volume',
            'amount': 'amount'
        }
        original_df = original_df.rename(columns=column_mapping)
        
        # 合并数据进行比较
        merged_df = pd.merge(
            qlib_df, 
            original_df[['date', 'open', 'high', 'low', 'close', 'volume', 'amount']],
            on='date',
            how='inner',
            suffixes=('_qlib', '_orig')
        )
        
        print(f"合并后的数据行数: {len(merged_df)}")
        
        # 计算差异
        for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
            merged_df[f'{field}_diff'] = merged_df[f'{field}_qlib'] - merged_df[f'{field}_orig']
        
        # 检查是否有差异
        has_diff = False
        for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
            diff_col = f'{field}_diff'
            if not np.allclose(merged_df[diff_col], 0, atol=1e-10):
                has_diff = True
                non_zero_diff = merged_df[merged_df[diff_col] != 0]
                print(f"\n{diff_col} 存在差异的行数: {len(non_zero_diff)}")
                if len(non_zero_diff) > 0:
                    print(f"差异统计: 最小值={non_zero_diff[diff_col].min()}, 最大值={non_zero_diff[diff_col].max()}, 平均值={non_zero_diff[diff_col].mean()}")
                    print("\n差异示例:")
                    print(non_zero_diff[['date', f'{field}_qlib', f'{field}_orig', diff_col]].head())
        
        if not has_diff:
            print("\n所有数据完全匹配!")
        
        return merged_df
    
    except Exception as e:
        print(f"从qlib获取数据时出错: {e}")
        return None

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        symbols = [sys.argv[1]]
    else:
        symbols = ["513980", "513120"]
    
    # 比较每个股票的数据
    for symbol in symbols:
        compare_qlib_data(symbol)
