#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交易策略模块，包含共享的交易策略逻辑
"""

import numpy as np
import logging
import pandas as pd
import datetime
import random

# 设置固定随机种子确保回测结果可重复
random.seed(42)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 交易参数默认值 - 优化版本
DEFAULT_STOP_LOSS_PCT = 0.025  # 进一步降低止损比例，更早止损
DEFAULT_TAKE_PROFIT_PCT = 0.050  # 提高止盈比例，让利润跑得更远
DEFAULT_TRAILING_STOP_PCT = 0.017  # 适当放宽跟踪止损比例，给价格更多波动空间
DEFAULT_MIN_TRADE_INTERVAL = 20  # 交易间隔设置为最优值
DEFAULT_SIGNAL_THRESHOLD = 4  # 提高信号确认阈值，减少假信号
DEFAULT_SIGNAL_CONFIRMATION_WINDOW = 6  # 减少信号确认窗口，对市场变化更敏感
DEFAULT_MAX_CONSECUTIVE_LOSS = 3  # 增加连续亏损容忍度，避免过早进入冷却期
DEFAULT_TREND_WINDOW = 8  # 降低趋势窗口大小，更敏感地捕捉趋势变化
DEFAULT_MARKET_STATE_WINDOW = 12  # 减少市场状态判断窗口，更快响应市场状态变化

class TradingStrategy:
    """
    交易策略类，包含共享的交易策略逻辑
    """
    # 类直接使用全局变量作为默认参数

    @staticmethod
    def calculate_tech_score(alpha158_features):
        """
        使用Alpha158特征计算技术指标综合得分 - 优化版本

        Parameters
        ----------
        alpha158_features : dict
            包含Alpha158特征的字典

        Returns
        -------
        int
            技术指标综合得分
        """
        # 检查是否所有特征都是默认值
        default_values = {
            'MA5': 1, 'MA10': 1, 'MA20': 1,
            'BETA5': 0, 'BETA10': 0, 'BETA20': 0,
            'RSQR10': 0, 'RESI10': 0,
            'CNTP10': 0, 'CNTN10': 0, 'CNTD10': 0,
            'SUMP5': 0.5, 'SUMP10': 0.5, 'SUMP20': 0.5,
            'SUMD5': 0, 'SUMD10': 0, 'SUMD20': 0,
            'STD5': 0, 'STD10': 0, 'STD20': 0
        }

        all_default = True
        for key, default_value in default_values.items():
            if alpha158_features.get(key, default_value) != default_value:
                all_default = False
                break

        if all_default:
            # 返回一个特殊的tech_score值，表示特征数据异常
            return 0  # 返回0而不是2，避免硬编码的2

        tech_score = 0

        # 提取需要的Alpha158特征
        # 趋势特征 (BETA - 价格变化率)
        short_trend = alpha158_features.get('BETA5', 0)
        medium_trend = alpha158_features.get('BETA10', 0)
        long_trend = alpha158_features.get('BETA20', 0)

        # 均线特征 (MA - 简单移动平均线)
        ma5 = alpha158_features.get('MA5', 1)
        ma10 = alpha158_features.get('MA10', 1)
        ma20 = alpha158_features.get('MA20', 1)
        close_price = 1  # 由于Alpha158特征已经除以了当前价格进行归一化，所以当前价格相对于自身为1

        # RSI类似特征 (SUMP - 类似于RSI的计算)
        sump5 = alpha158_features.get('SUMP5', 0.5)
        sump10 = alpha158_features.get('SUMP10', 0.5)
        sump20 = alpha158_features.get('SUMP20', 0.5)

        # 将SUMP转换为类似RSI的值 (RSI = 100 - 100/(1+RS)，其中RS = 上涨幅度/下跌幅度)
        # SUMP已经是上涨幅度占总幅度的比例，可以近似转换为RSI
        rsi_value = sump10 * 100  # 简单转换，将0-1的SUMP值映射到0-100的RSI值范围

        # MACD类似特征 (SUMD - 上涨和下跌的差异比率)
        sumd5 = alpha158_features.get('SUMD5', 0)
        sumd10 = alpha158_features.get('SUMD10', 0)
        sumd20 = alpha158_features.get('SUMD20', 0)

        # 波动率特征 (STD - 收盘价的标准差)
        std5 = alpha158_features.get('STD5', 0)
        std10 = alpha158_features.get('STD10', 0)
        std20 = alpha158_features.get('STD20', 0)

        # 其他有用的特征
        rsqr10 = alpha158_features.get('RSQR10', 0)  # 线性回归的R方值
        resi10 = alpha158_features.get('RESI10', 0)  # 线性回归的残差
        cntp10 = alpha158_features.get('CNTP10', 0)  # 过去10天中价格上涨的百分比
        cntn10 = alpha158_features.get('CNTN10', 0)  # 过去10天中价格下跌的百分比
        cntd10 = alpha158_features.get('CNTD10', 0)  # 上涨天数和下跌天数的差异

        # 新增：获取更多Alpha158特征
        kurtosis = alpha158_features.get('KURTOSIS20', 0)  # 峰度
        skewness = alpha158_features.get('SKEW20', 0)      # 偏度
        max_drawdown = alpha158_features.get('MAX_DRAWDOWN20', 0)  # 最大回撤

        # ========== 趋势得分 - 优化趋势识别的权重 ==========
        # 短期趋势 - 更平衡的阈值和权重
        if short_trend > 0.0003:  # 明显上涨趋势
            tech_score += 4  # 增加短期上涨权重
        elif short_trend > 0.0001:  # 轻微上涨趋势
            tech_score += 2  # 增加轻微上涨权重

        # 中期趋势 - 增加权重
        if medium_trend > 0.0001:  # 明显上涨趋势
            tech_score += 6  # 进一步增加中期趋势的权重
        elif medium_trend > 0.00005:  # 轻微上涨趋势
            tech_score += 3  # 增加轻微中期上涨权重

        # 长期趋势 - 大幅增加权重
        if long_trend > 0.00005:   # 明显上涨趋势
            tech_score += 7  # 进一步增加长期趋势的权重
        elif long_trend > 0.00002:  # 轻微上涨趋势
            tech_score += 4  # 增加轻微长期上涨权重

        # 下跌趋势惩罚 - 降低惩罚力度
        if short_trend < -0.0003:
            tech_score -= 1  # 减轻短期下跌惩罚
        elif short_trend < -0.0001:
            tech_score -= 0  # 轻微短期下跌不惩罚

        if medium_trend < -0.0001:
            tech_score -= 2  # 减轻中期下跌惩罚

        if long_trend < -0.00005:
            tech_score -= 3  # 减轻长期下跌惩罚

        # 趋势持续性评分 - 增强
        if short_trend > 0 and medium_trend > 0 and long_trend > 0:
            # 所有时间框架都是上涨趋势，信号更强
            trend_consistency = min(short_trend, medium_trend, long_trend) / max(0.00001, max(short_trend, medium_trend, long_trend))
            if trend_consistency > 0.7:  # 各时间框架趋势强度相近
                tech_score += 6  # 增加奖励
            else:
                tech_score += 4  # 增加奖励

        elif short_trend < 0 and medium_trend < 0 and long_trend < 0:
            # 所有时间框架都是下跌趋势，信号更强
            trend_consistency = min(abs(short_trend), abs(medium_trend), abs(long_trend)) / max(0.00001, max(abs(short_trend), abs(medium_trend), abs(long_trend)))
            if trend_consistency > 0.7:  # 各时间框架趋势强度相近
                tech_score -= 4  # 减轻惩罚
            else:
                tech_score -= 2  # 减轻惩罚

        # 趋势加速评分 - 增强
        if short_trend > medium_trend > long_trend > 0:
            # 趋势在加速上涨
            acceleration_strength = short_trend / max(0.00001, long_trend)
            if acceleration_strength > 2.0:  # 短期趋势是长期趋势的2倍以上
                tech_score += 7  # 增加强加速奖励
            else:
                tech_score += 5  # 增加一般加速奖励
                
        elif short_trend < medium_trend < long_trend < 0:
            # 趋势在加速下跌
            acceleration_strength = abs(short_trend) / max(0.00001, abs(long_trend))
            if acceleration_strength > 2.0:  # 短期下跌趋势是长期下跌趋势的2倍以上
                tech_score -= 5  # 减轻强加速下跌惩罚
            else:
                tech_score -= 3  # 减轻一般加速下跌惩罚

        # ========== 均线系统得分 - 大幅增加均线关系的权重 ==========
        # 注意：Alpha158中的MA特征是Mean($close, X)/$close，所以值越小表示均线越高
        if ma5 < ma10 and ma10 < ma20:  # 多头排列
            tech_score += 12  # 进一步增加多头排列的权重
        elif ma5 < ma10:  # 短期均线上穿中期均线
            tech_score += 7  # 增加均线金叉权重
        elif ma10 < ma20:  # 中期均线上穿长期均线
            tech_score += 6  # 增加中长期均线金叉权重

        # 价格与均线关系
        if close_price < ma5:  # 当前价格低于5日均线，可能超卖
            tech_score += 1  # 略微增加超卖信号权重
        elif close_price > ma5:  # 当前价格高于5日均线
            tech_score += 5  # 增加价格强势信号权重

        # ========== RSI得分 - 更平衡的区间划分 ==========
        if 45 < rsi_value < 55:  # 中性区域，更窄的范围
            tech_score += 2  # 增加中性区域权重
        elif 35 < rsi_value < 45:  # 轻微超卖
            tech_score += 4  # 增加轻微超卖权重
        elif 25 < rsi_value < 35:  # 中度超卖
            tech_score += 5  # 增加中度超卖权重
        elif rsi_value <= 25:  # 严重超卖，强反弹信号
            tech_score += 6  # 增加严重超卖权重
        elif 55 < rsi_value < 65:  # 轻微超买
            tech_score += 1  # 增加轻微超买权重，改为略微正面
        elif 65 < rsi_value < 75:  # 中度超买
            tech_score -= 1  # 减轻中度超买惩罚
        elif rsi_value >= 75:  # 严重超买
            tech_score -= 3  # 减轻严重超买惩罚

        # ========== MACD类似得分 - 增加MACD信号的权重 ==========
        if sumd5 > 0 and sumd10 > 0 and sumd20 > 0:  # 多周期共振
            tech_score += 8  # 增加多周期共振的权重
        elif sumd10 > 0.15:  # 强势上涨
            tech_score += 7  # 增加强势上涨的权重
        elif sumd10 > 0.05:  # 中等上涨
            tech_score += 4  # 增加中等上涨权重
        elif sumd10 > 0:  # 轻微上涨
            tech_score += 2  # 增加轻微上涨权重

        if sumd5 < 0 and sumd10 < 0 and sumd20 < 0:  # 多周期共振下跌
            tech_score -= 5  # 减轻多周期共振下跌的惩罚      
        elif sumd10 < -0.15:  # 强势下跌
            tech_score -= 4  # 减轻强势下跌惩罚
        elif sumd10 < -0.05:  # 中等下跌
            tech_score -= 2  # 减轻中等下跌惩罚

        # ========== 波动率得分 - 更精细的评估 ==========
        # 低波动率通常意味着盘整，可能即将突破
        if std5 < 0.005 and std10 < 0.008:  # 极低波动率
            tech_score += 2  # 增加极低波动率权重
        elif std10 < 0.01:  # 低波动率
            tech_score += 1  # 增加低波动率权重
        # 高波动率可能意味着不稳定，但也可能是强势趋势
        elif std10 > 0.03 and sumd10 > 0:  # 高波动率 + 上涨趋势 = 强势上涨
            tech_score += 4  # 增加高波动率上涨的权重      
        elif std10 > 0.03 and sumd10 < 0:  # 高波动率 + 下跌趋势 = 强势下跌
            tech_score -= 2  # 减轻高波动率下跌惩罚
        elif std10 > 0.04:  # 极高波动率，不稳定
            tech_score -= 0  # 极高波动率保持中性

        # ========== 上涨/下跌天数比例 - 增加权重 ==========
        if cntd10 > 0.4:  # 上涨天数明显多于下跌天数
            tech_score += 6  # 增加上涨天数多的权重      
        elif cntd10 > 0.2:  # 上涨天数较多
            tech_score += 4  # 增加上涨天数较多的权重
        elif cntd10 < -0.4:  # 下跌天数明显多于上涨天数
            tech_score -= 4  # 减轻下跌天数多的惩罚      
        elif cntd10 < -0.2:  # 下跌天数较多
            tech_score -= 2  # 减轻下跌天数较多的惩罚

        # ========== 趋势线性度 - 高R方值表示趋势更明确 ==========
        if rsqr10 > 0.8:  # 趋势线性度非常高
            tech_score += 5  # 增加高线性度的权重      
        elif rsqr10 > 0.6:  # 趋势线性度高
            tech_score += 3  # 增加较高线性度的权重

        # ========== 残差 - 偏离趋势线的程度 ==========
        if resi10 < -0.01 and short_trend > 0:  # 价格低于趋势线但趋势向上
            tech_score += 5  # 增加此类买入机会的权重  
        elif resi10 > 0.01 and short_trend < 0:  # 价格高于趋势线但趋势向下
            tech_score -= 3  # 减轻此类卖出机会的惩罚

        # ========== 多个指标的综合信号 - 大幅增加综合信号的权重 ==========
        # RSI超卖 + 价格在均线下方 + 上涨天数增加 = 强买入信号
        if rsi_value < 30 and close_price < ma5 and cntd10 > 0:
            tech_score += 7  # 增加强买入信号的权重      

        # RSI超买 + 价格在均线上方 + 下跌天数增加 = 强卖出信号
        if rsi_value > 70 and close_price > ma5 and cntd10 < 0:
            tech_score -= 5  # 减轻强卖出信号的惩罚      

        # ========== 峰度和偏度 - 优化 ==========
        # 正偏度通常意味着正向收益的可能性更大
        if skewness > 0.5:  # 显著正偏度
            tech_score += 4  # 增加显著正偏度权重
        elif skewness > 0.2:  # 轻微正偏度
            tech_score += 2  # 增加轻微正偏度权重
        elif skewness < -0.5:  # 显著负偏度
            tech_score -= 2  # 减轻显著负偏度惩罚
        elif skewness < -0.2:  # 轻微负偏度
            tech_score -= 1  # 减轻轻微负偏度惩罚

        # 峰度反映分布的"尖锐度"，高峰度表示极端值出现的可能性更高
        if kurtosis > 3:  # 高峰度
            if sumd10 > 0:  # 在上升趋势中，高峰度可能意味着加速上涨
                tech_score += 3  # 增加上升趋势高峰度权重
            else:  # 在下降趋势中，高峰度可能意味着加速下跌
                tech_score -= 1  # 减轻下降趋势高峰度惩罚

        # ========== 最大回撤 - 增加权重 ==========
        if max_drawdown < -0.05:  # 最大回撤较大，可能已超卖
            tech_score += 4  # 增加最大回撤权重

        return tech_score

    @staticmethod
    def calculate_price_trend(price_trend):
        """
        计算价格趋势

        Parameters
        ----------
        price_trend : list
            价格趋势列表

        Returns
        -------
        tuple
            (price_trend_up, price_trend_down, short_trend, medium_trend, long_trend)
        """
        price_trend_up = False
        price_trend_down = False
        short_trend = 0
        medium_trend = 0
        long_trend = 0

        if len(price_trend) >= 5:
            # 计算短期趋势（最近5个点）
            short_trend = np.polyfit(range(5), price_trend[-5:], 1)[0]
            price_trend_up = short_trend > 0.0008  # 上升趋势，增加阈值
            price_trend_down = short_trend < -0.0008  # 下降趋势，增加阈值

        if len(price_trend) >= 10:
            # 计算中期趋势（最近10个点）
            medium_trend = np.polyfit(range(10), price_trend[-10:], 1)[0]

        if len(price_trend) >= 20:
            # 计算长期趋势（最近20个点）
            long_trend = np.polyfit(range(20), price_trend[-20:], 1)[0]

        return price_trend_up, price_trend_down, short_trend, medium_trend, long_trend

    @staticmethod
    def update_market_state(price_trend, market_state_history, current_market_state, market_state_window=None):
        """
        更新市场状态 - 优化版本，更精确的市场状态判断，增强对趋势变化的敏感度

        Parameters
        ----------
        price_trend : list
            价格趋势列表
        market_state_history : list
            市场状态历史
        current_market_state : str
            当前市场状态
        market_state_window : int, optional
            市场状态判断窗口, by default None (使用全局变量)

        Returns
        -------
        tuple
            (new_market_state, updated_market_state_history)
        """
        # 如果未指定市场状态窗口，使用全局变量
        if market_state_window is None:
            market_state_window = DEFAULT_MARKET_STATE_WINDOW

        # 如果价格趋势列表为空或数据不足，保持当前市场状态
        if not price_trend or len(price_trend) < 5:
            return current_market_state, market_state_history

        # 计算短期趋势 (最近5个点)
        short_trend = np.polyfit(range(min(5, len(price_trend))), price_trend[-min(5, len(price_trend)):], 1)[0]

        # 计算中期趋势 (最近10个点，如果有足够数据)
        if len(price_trend) >= 10:
            medium_trend = np.polyfit(range(10), price_trend[-10:], 1)[0]
        else:
            medium_trend = short_trend  # 如果没有足够数据，使用短期趋势

        # 计算长期趋势 (最近20个点，如果有足够数据)
        if len(price_trend) >= 20:
            long_trend = np.polyfit(range(20), price_trend[-20:], 1)[0]
        else:
            long_trend = medium_trend  # 如果没有足够数据，使用中期趋势

        # 计算价格波动性 (最近10个点的标准差，如果有足够数据)
        if len(price_trend) >= 10:
            volatility = np.std(price_trend[-10:])
        else:
            volatility = np.std(price_trend)  # 使用所有可用数据

        # 计算价格动量 (最近5个点的变化率总和)
        if len(price_trend) >= 6:  # 需要至少6个点才能计算5个变化率
            momentum = sum([(price_trend[-i] - price_trend[-i-1]) for i in range(1, 6)])
        else:
            momentum = 0

        # 计算价格与趋势线的偏离度 (残差)
        if len(price_trend) >= 10:
            trend_line = np.polyval(np.polyfit(range(10), price_trend[-10:], 1), range(10))
            deviation = price_trend[-1] - trend_line[-1]
        else:
            deviation = 0

        # 计算短期趋势的加速度 (二阶导数)
        if len(price_trend) >= 8:
            # 计算最近8个点的多项式拟合（2阶）
            polynomial = np.polyfit(range(8), price_trend[-8:], 2)
            acceleration = 2 * polynomial[0]  # 二阶项系数乘以2
        else:
            acceleration = 0

        # 新增：计算波动率变化率
        if len(price_trend) >= 15:
            recent_volatility = np.std(price_trend[-5:])
            prev_volatility = np.std(price_trend[-15:-5])
            volatility_change = (recent_volatility - prev_volatility) / max(0.00001, prev_volatility)
        else:
            volatility_change = 0

        # 新增：计算价格走势斜率变化 - 短期趋势斜率与中期趋势斜率的比较
        if len(price_trend) >= 10:
            slope_change = short_trend - medium_trend
        else:
            slope_change = 0

        # 根据多个指标综合判断当前市场状态
        new_market_state = "neutral"  # 默认为中性

        # 牛市条件 - 更敏感的判断
        bullish_conditions = [
            # 强牛市条件 - 至少满足两个条件
            (short_trend > 0.0002 and medium_trend > 0.0001 and long_trend > 0.00005),  # 三周期上升
            (medium_trend > 0.0001 and acceleration > 0.00001),  # 趋势加速向上
            (momentum > 0.001 and short_trend > 0.0001),  # 强劲上涨动量
            (long_trend > 0.00008 and volatility < 0.01 * np.mean(price_trend[-20:] if len(price_trend) >= 20 else price_trend)),  # 稳定上涨
            (slope_change > 0.00005 and short_trend > 0),  # 短期斜率高于中期斜率且为正
            (volatility_change < -0.15 and short_trend > 0)  # 波动率下降而价格上涨（稳定上涨）
        ]

        # 熊市条件 - 更敏感的判断
        bearish_conditions = [
            # 强熊市条件 - 至少满足两个条件
            (short_trend < -0.0002 and medium_trend < -0.0001 and long_trend < -0.00005),  # 三周期下降
            (medium_trend < -0.0001 and acceleration < -0.00001),  # 趋势加速向下
            (momentum < -0.001 and short_trend < -0.0001),  # 强劲下跌动量
            (short_trend < -0.0003 and volatility > 0.015 * np.mean(price_trend[-10:] if len(price_trend) >= 10 else price_trend)),  # 急剧下跌
            (slope_change < -0.00005 and short_trend < 0),  # 短期斜率低于中期斜率且为负
            (volatility_change > 0.2 and short_trend < 0)  # 波动率上升而价格下跌（剧烈下跌）
        ]

        # 趋势反转条件 - 识别潜在的趋势反转，更敏感
        trend_reversal_conditions = [
            # 牛转熊 - 满足任一条件
            (short_trend < -0.0002 and medium_trend > 0 and momentum < -0.0008),  # 短期开始下跌
            (acceleration < -0.00002 and medium_trend > 0),  # 加速度变负
            (volatility_change > 0.3 and short_trend < 0 and medium_trend > 0),  # 波动率大幅上升且短期下跌

            # 熊转牛 - 满足任一条件
            (short_trend > 0.0002 and medium_trend < 0 and momentum > 0.0008),  # 短期开始上涨
            (acceleration > 0.00002 and medium_trend < 0),  # 加速度变正
            (deviation > 0.01 * np.mean(price_trend) and short_trend > 0 and medium_trend < 0)  # 价格大幅偏离趋势线
        ]

        # 计算满足条件的数量
        bullish_count = sum(1 for cond in bullish_conditions if cond)
        bearish_count = sum(1 for cond in bearish_conditions if cond)
        reversal_count = sum(1 for cond in trend_reversal_conditions if cond)

        # 确定市场状态 - 降低判断阈值，更敏感
        if bullish_count >= 2 and bearish_count == 0:
            new_market_state = "bullish"  # 明确牛市
        elif bearish_count >= 2 and bullish_count == 0:
            new_market_state = "bearish"  # 明确熊市
        elif reversal_count >= 1:
            # 趋势可能反转，短期内保持中性观望
            new_market_state = "neutral"
        else:
            # 根据长期趋势微调
            if long_trend > 0.00005:
                new_market_state = "neutral_bullish"  # 中性偏牛
            elif long_trend < -0.00005:
                new_market_state = "neutral_bearish"  # 中性偏熊
            else:
                new_market_state = "neutral"  # 纯中性

        # 简化市场状态，将中性偏牛和中性偏熊合并到中性
        if new_market_state in ["neutral_bullish", "neutral_bearish"]:
            new_market_state = "neutral"

        # 更新市场状态历史
        market_state_history.append(new_market_state)
        if len(market_state_history) > market_state_window:
            market_state_history.pop(0)

        # 根据历史市场状态确定最终市场状态，使用加权投票
        # 最近的状态权重更高
        weights = np.linspace(0.6, 1.0, len(market_state_history))  # 权重从0.6到1.0线性增加
        bullish_weight = sum(weights[i] for i, state in enumerate(market_state_history) if state == "bullish")
        bearish_weight = sum(weights[i] for i, state in enumerate(market_state_history) if state == "bearish")
        neutral_weight = sum(weights[i] for i, state in enumerate(market_state_history) if state == "neutral")

        total_weight = sum(weights)

        # 调整牛熊市的判断阈值，降低以更快响应市场变化
        if bullish_weight / total_weight > 0.25:  # 降低牛市阈值
            new_market_state = "bullish"
        elif bearish_weight / total_weight > 0.25:  # 降低熊市阈值
            new_market_state = "bearish"
        else:
            new_market_state = "neutral"

        # 市场状态平滑：避免频繁切换
        # 如果当前状态与新状态不同，且新状态在历史中占比不足15%，则保持当前状态（降低阈值使转换更灵敏）
        if current_market_state != new_market_state:
            new_state_count = market_state_history.count(new_market_state)
            if new_state_count / len(market_state_history) < 0.15:  # 大幅降低阈值
                # 保持当前状态不变
                return current_market_state, market_state_history

        return new_market_state, market_state_history

    @staticmethod
    def generate_buy_signal(tech_score, price_trend_up, current_ma5, current_ma10, current_ma20,
                           current_rsi, market_state, tech_threshold=3, current_price=None):
        """
        生成买入信号 - 优化版本，更积极捕捉上涨机会

        Parameters
        ----------
        tech_score : int
            技术指标综合得分
        price_trend_up : bool
            价格趋势是否向上
        current_ma5 : float
            5日均线
        current_ma10 : float
            10日均线
        current_ma20 : float
            20日均线
        current_rsi : float
            RSI指标
        market_state : str
            市场状态
        tech_threshold : int, optional
            技术得分阈值, by default 3
        current_price : float, optional
            当前价格, by default None

        Returns
        -------
        bool
            是否生成买入信号
        """
        # 根据市场状态调整技术得分阈值 - 平衡的阈值
        if market_state == "bullish":
            tech_threshold = 3  # 大幅降低牛市阈值，更积极捕捉上涨
        elif market_state == "bearish":
            tech_threshold = 6  # 保持熊市的较高阈值，但略有降低
        else:
            tech_threshold = 4  # 中性市场使用适中阈值

        # 如果未提供当前价格，则设置为一个大于均线的值，以避免影响条件判断
        if current_price is None:
            current_price = max(current_ma5, current_ma10, current_ma20) * 1.01

        # 买入信号：平衡的条件组合
        # 基本买入条件 - 适中的技术得分要求
        basic_buy_condition = tech_score >= tech_threshold

        # RSI条件 - 较宽松的RSI范围
        rsi_condition = 25 <= current_rsi <= 70  # 扩大RSI范围，包含更多买入机会

        # 熊市特殊条件 - 在熊市中需要较强的反转信号，但不要过于严格
        bearish_condition = market_state != "bearish" or (current_rsi < 35 and tech_score >= 7)

        # 价格趋势条件 - 更积极的趋势确认
        price_trend_condition = (
            (price_trend_up) or  # 只要价格处于上升趋势就可以
            (current_ma5 > current_ma10 and current_price > current_ma5) or  # 短期均线在中期均线上方且价格在短期均线上方
            (current_rsi < 35 and tech_score >= 5 and price_trend_up)  # 超卖+较高技术得分+上升趋势
        )

        # 均线关系条件 - 更宽松的均线排列要求
        ma_condition = (current_ma5 >= current_ma10 * 0.998) or (current_ma10 >= current_ma20 * 0.998 and tech_score >= 5)

        # 强制买入条件 - 扩大特殊情况触发范围
        force_buy_condition = (
            (current_rsi < 30 and tech_score >= 8 and price_trend_up) or  # 严重超卖 + 较高技术得分 + 价格已开始上升
            (tech_score >= 10) or  # 技术得分非常高
            (current_rsi < 25 and price_trend_up) or  # 极度超卖 + 价格上涨
            (current_ma5 > current_ma10 and current_ma10 > current_ma20 and current_price > current_ma5 and tech_score >= 5)  # 完美多头排列 + 中等技术得分
        )

        # 牛市特殊条件 - 在牛市中更积极买入
        if market_state == "bullish":
            bullish_special_condition = (
                (current_price > current_ma5 and current_ma5 > current_ma10 and tech_score >= 2) or  # 牛市中价格在短均线上方即可考虑买入
                (tech_score >= 4)  # 牛市中只要技术得分适中就考虑买入
            )
        else:
            bullish_special_condition = False

        # 综合买入信号 - 更灵活的条件组合
        buy_signal = force_buy_condition or bullish_special_condition or (
            basic_buy_condition and
            rsi_condition and
            (bearish_condition) and
            (price_trend_condition or ma_condition)  # 价格趋势条件和均线条件只需满足一个
        )

        return buy_signal

    @staticmethod
    def generate_sell_signal(tech_score, price_trend_down, current_ma5, current_ma10, current_ma20,
                            current_rsi, market_state, current_price=None):
        """
        生成卖出信号 - 优化版本，在牛市中更宽容，让利润跑得更远

        Parameters
        ----------
        tech_score : int
            技术指标综合得分
        price_trend_down : bool
            价格趋势是否向下
        current_ma5 : float
            5日均线
        current_ma10 : float
            10日均线
        current_ma20 : float
            20日均线
        current_rsi : float
            RSI指标
        market_state : str
            市场状态
        current_price : float, optional
            当前价格, by default None

        Returns
        -------
        bool
            是否生成卖出信号
        """
        # 如果未提供当前价格，则设置为一个小于均线的值，以避免影响条件判断
        if current_price is None:
            current_price = min(current_ma5, current_ma10, current_ma20) * 0.99

        # 根据市场状态调整技术得分卖出阈值 - 更平衡的阈值
        if market_state == "bullish":
            # 牛市中提高技术得分卖出阈值，更难触发卖出
            tech_sell_threshold = -7
        elif market_state == "bearish":
            # 熊市中降低技术得分卖出阈值，更容易触发卖出
            tech_sell_threshold = -1
        else:
            # 中性市场使用中等阈值
            tech_sell_threshold = -5

        # 卖出信号：更平衡的条件组合
        # 基本卖出条件 - 降低触发频率
        basic_sell_condition = tech_score <= tech_sell_threshold

        # 均线死叉条件 - 牛市中要求更明确的死叉信号
        if market_state == "bullish":
            death_cross_condition = (
                (current_ma5 < current_ma10 * 0.985 and price_trend_down) or  # 短期均线明显下穿中期均线
                (current_ma10 < current_ma20 * 0.985 and price_trend_down)     # 中期均线明显下穿长期均线
            )
        else:
            death_cross_condition = (
                (current_ma5 < current_ma10 * 0.99 and price_trend_down) or  # 短期均线明显下穿中期均线
                (current_ma10 < current_ma20 * 0.99 and price_trend_down)     # 中期均线明显下穿长期均线
            )

        # RSI超买条件 - 根据市场状态调整RSI阈值
        if market_state == "bullish":
            # 牛市中提高RSI超买阈值，更难触发卖出
            rsi_overbought_condition = current_rsi > 85
        elif market_state == "bearish":
            # 熊市中降低RSI超买阈值，更容易触发卖出
            rsi_overbought_condition = current_rsi > 68
        else:
            # 中性市场使用中等阈值
            rsi_overbought_condition = current_rsi > 75

        # 价格与均线关系条件 - 牛市中要求更严格的跌破条件
        if market_state == "bullish":
            price_ma_condition = (
                (current_price < current_ma5 * 0.97 and current_ma5 > current_ma10) or  # 价格显著跌破短期均线
                (current_price < current_ma10 * 0.97 and tech_score < -2)                # 价格显著跌破中期均线
            )
        else:
            price_ma_condition = (
                (current_price < current_ma5 * 0.98 and current_ma5 > current_ma10) or  # 价格明显跌破短期均线
                (current_price < current_ma10 * 0.98 and tech_score < -2)                # 价格明显跌破中期均线
            )

        # 趋势反转条件 - 要求更明确的反转信号
        trend_reversal_condition = (
            price_trend_down and
            current_price < current_ma5 * 0.98 and  # 价格明显跌破短期均线
            (tech_score < -3 or current_rsi > 70)
        )

        # 强制卖出条件 - 特定情况下无论其他条件如何都卖出，但提高阈值降低触发频率
        # 在牛市中进一步提高强制卖出阈值
        if market_state == "bullish":
            force_sell_condition = (
                (current_rsi > 90) or  # RSI极度超买
                (tech_score <= -9) or  # 技术得分极低
                (price_trend_down and current_rsi > 82 and tech_score < -6)  # 下跌趋势 + 极度超买 + 明显负技术得分
            )
        else:
            force_sell_condition = (
                (current_rsi > 85) or  # RSI极度超买
                (tech_score <= -7) or  # 技术得分极低
                (price_trend_down and current_rsi > 75 and tech_score < -4)  # 下跌趋势 + 明显超买 + 明显负技术得分
            )

        # 综合卖出信号 - 至少满足两个条件，或者强制卖出条件
        multiple_conditions = [
            basic_sell_condition,
            death_cross_condition,
            rsi_overbought_condition,
            price_ma_condition,
            trend_reversal_condition
        ]

        # 计算满足的条件数量
        conditions_met = sum(1 for cond in multiple_conditions if cond)

        # 调整条件数量要求 - 牛市中需要更多条件触发卖出
        if market_state == "bullish":
            required_conditions = 3  # 牛市中需要满足更多条件
        elif market_state == "bearish":
            required_conditions = 1  # 熊市中满足任一条件即可
        else:
            required_conditions = 2  # 中性市场维持两个条件

        # 满足条件数量要求，或者满足强制卖出条件
        sell_signal = (conditions_met >= required_conditions) or force_sell_condition

        return sell_signal

    @staticmethod
    def generate_stop_signals(current_price, last_trade_price, max_price_since_buy,
                             stop_loss_pct=None, take_profit_pct=None, trailing_stop_pct=None, market_state=None):
        """
        生成止损、止盈和跟踪止损信号 - 增强版本，更智能的止损止盈策略，根据市场状态调整

        Parameters
        ----------
        current_price : float
            当前价格
        last_trade_price : float
            上次交易价格
        max_price_since_buy : float
            买入后的最高价格
        stop_loss_pct : float, optional
            止损比例, by default None (使用全局变量)
        take_profit_pct : float, optional
            止盈比例, by default None (使用全局变量)
        trailing_stop_pct : float, optional
            跟踪止损比例, by default None (使用全局变量)
        market_state : str, optional
            市场状态, by default None (用于动态调整止损止盈比例)

        Returns
        -------
        tuple
            (stop_loss_signal, take_profit_signal, trailing_stop_signal)
        """
        # 如果未指定参数，使用全局变量
        if stop_loss_pct is None:
            stop_loss_pct = DEFAULT_STOP_LOSS_PCT
        if take_profit_pct is None:
            take_profit_pct = DEFAULT_TAKE_PROFIT_PCT
        if trailing_stop_pct is None:
            trailing_stop_pct = DEFAULT_TRAILING_STOP_PCT

        # 如果没有上次交易价格，则不生成信号
        if last_trade_price is None:
            return False, False, False

        # 根据市场状态调整止损止盈参数
        if market_state == "bullish":
            # 牛市：放宽止损，提高止盈目标，放宽跟踪止损
            stop_loss_pct = stop_loss_pct * 1.3  # 增加止损距离
            take_profit_pct = take_profit_pct * 1.5  # 提高止盈目标
            trailing_stop_pct = trailing_stop_pct * 1.4  # 放宽跟踪止损
        elif market_state == "bearish":
            # 熊市：收紧止损，降低止盈目标，收紧跟踪止损
            stop_loss_pct = stop_loss_pct * 0.7  # 减少止损距离
            take_profit_pct = take_profit_pct * 0.7  # 降低止盈目标
            trailing_stop_pct = trailing_stop_pct * 0.6  # 收紧跟踪止损

        # 计算价格变化百分比
        price_change_pct = (current_price - last_trade_price) / last_trade_price

        # 计算最大收益百分比
        max_profit_pct = 0
        if max_price_since_buy > 0:
            max_profit_pct = (max_price_since_buy - last_trade_price) / last_trade_price

        # 计算从最高点的回撤百分比
        drawdown_pct = 0
        if max_price_since_buy > 0:
            drawdown_pct = (current_price - max_price_since_buy) / max_price_since_buy

        # ===== 止损信号 =====
        # 基础止损: 价格下跌超过止损比例
        basic_stop_loss = price_change_pct <= -stop_loss_pct

        # 增强型止损: 针对利润回吐情况
        profit_giving_back_stop_loss = False
        if max_profit_pct > take_profit_pct * 0.8:  # 曾经接近止盈目标
            # 从高点回撤程度随接近止盈目标的程度增加
            max_allowed_drawdown = trailing_stop_pct * (1.0 - min(max_profit_pct / take_profit_pct, 0.9))
            profit_giving_back_stop_loss = drawdown_pct <= -max_allowed_drawdown

        # 市场特定止损
        market_specific_stop_loss = False
        if market_state == "bearish" and price_change_pct < 0:
            # 熊市中，亏损加速时提前止损
            accel_stop_loss_threshold = -stop_loss_pct * 0.75  # 熊市中提前止损
            market_specific_stop_loss = price_change_pct <= accel_stop_loss_threshold
        
        # 牛市中特殊止损：更宽容的止损
        if market_state == "bullish" and price_change_pct < 0:
            # 牛市中曾有较大收益，此时加大止损保护
            if max_profit_pct > take_profit_pct * 0.5:
                # 有50%止盈目标以上的收益，保护利润
                profit_protection_threshold = -stop_loss_pct * 0.85
                if price_change_pct <= profit_protection_threshold:
                    market_specific_stop_loss = True
                    
            # 牛市中如果没有明显收益，使用正常止损
            else:
                market_specific_stop_loss = False

        # 综合止损信号
        stop_loss_signal = basic_stop_loss or profit_giving_back_stop_loss or market_specific_stop_loss

        # ===== 止盈信号 =====
        # 基础止盈: 价格上涨超过止盈比例
        basic_take_profit = price_change_pct >= take_profit_pct

        # 快速收益止盈: 短时间内获得较大收益时提早获利了结
        quick_profit_take = False
        # 此处省略快速收益检测，因为没有可用的持有时间信息

        # 市场特定止盈
        market_specific_take_profit = False
        if market_state == "bearish" and price_change_pct > take_profit_pct * 0.7:
            # 熊市中接近止盈目标就获利了结
            market_specific_take_profit = True
        elif market_state == "bullish" and price_change_pct > take_profit_pct * 1.3:
            # 牛市中需要超过止盈目标较多才获利了结，让利润跑
            market_specific_take_profit = True
            
        # 提高止盈条件：如果连续上涨过快，提前止盈锁定利润
        rapid_profit_take = False
        if max_profit_pct > take_profit_pct * 0.8 and market_state != "bullish":
            # 非牛市中快速接近止盈目标，锁定部分利润
            rapid_profit_take = True

        # 综合止盈信号
        take_profit_signal = basic_take_profit or quick_profit_take or market_specific_take_profit or rapid_profit_take

        # ===== 跟踪止损信号 =====
        trailing_stop_signal = False

        # 只有当价格曾经上涨一定幅度后才启用跟踪止损
        if market_state == "bullish":
            # 牛市中启用跟踪止损的门槛更高
            trailing_stop_enabled = max_profit_pct >= 0.035  # 至少3.5%的收益
        else:
            trailing_stop_enabled = max_profit_pct >= 0.025  # 至少2.5%的收益

        if trailing_stop_enabled:
            # 动态跟踪止损比例：根据最大收益率自动调整
            # 收益越高，跟踪止损越紧，保护已有利润
            dynamic_trailing_stop = trailing_stop_pct  # 默认跟踪止损比例

            # 基于最大收益动态调整跟踪止损比例
            if max_profit_pct >= take_profit_pct * 1.5:  # 如果收益率超过止盈目标的1.5倍
                dynamic_trailing_stop *= 0.7  # 更紧的跟踪止损
            elif max_profit_pct >= take_profit_pct:  # 如果收益率超过止盈目标
                dynamic_trailing_stop *= 0.8  # 较紧的跟踪止损
            elif max_profit_pct >= take_profit_pct * 0.5:  # 如果收益率达到止盈目标的一半
                dynamic_trailing_stop *= 0.9  # 稍紧的跟踪止损

            # 基于市场状态进一步调整
            if market_state == "bearish":
                dynamic_trailing_stop *= 0.7  # 熊市更紧的跟踪止损
            elif market_state == "bullish":
                dynamic_trailing_stop *= 1.2  # 牛市更宽松的跟踪止损

            # 从高点回撤超过动态跟踪止损比例触发信号
            trailing_stop_signal = drawdown_pct <= -dynamic_trailing_stop

            # 额外保护：如果已经实现了显著收益且接近止盈目标，增加保护性止损
            if max_profit_pct >= take_profit_pct * 0.95:
                protective_stop = dynamic_trailing_stop * 0.7  # 更紧的保护性止损
                if drawdown_pct <= -protective_stop:
                    trailing_stop_signal = True
                    
            # 牛市中的特殊跟踪止损：如果已经跑出很大收益，提供额外保护
            if market_state == "bullish" and max_profit_pct >= take_profit_pct * 1.3:
                bull_protective_stop = dynamic_trailing_stop * 0.8
                if drawdown_pct <= -bull_protective_stop:
                    trailing_stop_signal = True

        return stop_loss_signal, take_profit_signal, trailing_stop_signal

    @staticmethod
    def calculate_sell_portion():
        """
        计算卖出比例 - 始终返回1.0（全部卖出）

        Returns
        -------
        float
            卖出比例
        """
        # 始终全部卖出
        return 1.0

    @staticmethod
    def calculate_position_pct(market_state=None, tech_score=None):
        """
        计算仓位百分比 - 根据市场状态和技术得分动态调整仓位

        Parameters
        ----------
        market_state : str, optional
            市场状态, by default None
        tech_score : int, optional
            技术得分, by default None

        Returns
        -------
        float
            仓位百分比
        """
        # 获取基于市场状态的默认仓位
        if market_state is None:
            market_state = "neutral"

        # 获取市场状态对应的阈值参数
        thresholds = TradingStrategy.get_thresholds_by_market_state(market_state)
        position_pct = thresholds["position_pct"]

        # 如果提供了技术得分，进一步根据技术得分调整仓位
        if tech_score is not None:
            if market_state == "bullish":
                # 牛市中，技术得分越高，仓位越大
                if tech_score >= 8:
                    position_pct = 1.0  # 满仓
                elif tech_score >= 6:
                    position_pct = min(position_pct * 1.1, 1.0)  # 增加10%仓位，但不超过满仓
                elif tech_score <= 2:
                    position_pct = position_pct * 0.9  # 减少10%仓位
            elif market_state == "bearish":
                # 熊市中，技术得分越低，仓位越小
                if tech_score <= 3:
                    position_pct = position_pct * 0.8  # 大幅减少仓位
                elif tech_score <= 5:
                    position_pct = position_pct * 0.9  # 减少仓位
                elif tech_score >= 9:
                    position_pct = min(position_pct * 1.2, 0.9)  # 熊市中高技术得分，增加仓位但不超过90%
            else:  # neutral
                # 中性市场，技术得分适中时维持默认仓位，极端值时调整
                if tech_score >= 8:
                    position_pct = min(position_pct * 1.1, 1.0)  # 增加仓位但不超过满仓
                elif tech_score <= 2:
                    position_pct = position_pct * 0.9  # 减少仓位

        return position_pct

    @staticmethod
    def update_cooling_period(profit_pct, consecutive_loss_count, current_index, max_consecutive_loss=None, market_state=None, tech_score=None):
        """
        更新冷却期 - 增强版本，更智能的冷却期调整策略，根据市场状态和交易结果动态调整

        Parameters
        ----------
        profit_pct : float
            收益率
        consecutive_loss_count : int
            连续亏损计数
        current_index : int
            当前索引
        max_consecutive_loss : int, optional
            最大连续亏损次数, by default None (使用全局变量)
        market_state : str, optional
            市场状态, by default None (用于动态调整冷却期)
        tech_score : int, optional
            技术得分, by default None (用于动态调整冷却期)

        Returns
        -------
        tuple
            (new_consecutive_loss_count, cooling_period)
        """
        # 如果未指定最大连续亏损次数，使用全局变量
        if max_consecutive_loss is None:
            max_consecutive_loss = DEFAULT_MAX_CONSECUTIVE_LOSS
        cooling_period = 0

        # 调整熊市中的容忍度
        adjusted_max_loss = max_consecutive_loss
        if market_state == "bearish":
            adjusted_max_loss = max(2, max_consecutive_loss - 1)  # 熊市降低容忍度
        elif market_state == "bullish":
            adjusted_max_loss = max_consecutive_loss + 1  # 牛市增加容忍度

        if profit_pct < 0:
            # 根据亏损幅度和市场状态动态调整连续亏损计数
            loss_severity = abs(profit_pct)

            # 轻微亏损 (低于1%)
            if loss_severity < 0.01:
                # 熊市中即使轻微亏损也更谨慎
                loss_increment = 0.25 if market_state != "bearish" else 0.5
                consecutive_loss_count += loss_increment
            # 中度亏损 (1%-2%)
            elif loss_severity < 0.02:
                loss_increment = 0.75 if market_state != "bearish" else 1.0
                consecutive_loss_count += loss_increment
            # 较大亏损 (2%-3%)
            elif loss_severity < 0.03:
                loss_increment = 1.0 if market_state != "bearish" else 1.25
                consecutive_loss_count += loss_increment
            # 严重亏损 (大于3%)
            else:
                loss_increment = 1.5 if market_state != "bearish" else 2.0
                consecutive_loss_count += loss_increment

            # 根据连续亏损计数设置冷却期
            if consecutive_loss_count >= adjusted_max_loss:
                # 动态冷却期：根据亏损幅度、市场状态和连续亏损次数动态调整
                if market_state == "bearish":
                    # 熊市中更长的冷却期
                    cooling_factor = 5 if loss_severity >= 0.03 else 4
                elif market_state == "bullish":
                    # 牛市中更短的冷却期
                    cooling_factor = 3 if loss_severity >= 0.03 else 2
                else:
                    # 中性市场中适中的冷却期
                    cooling_factor = 4 if loss_severity >= 0.03 else 3

                # 将技术得分纳入冷却期计算
                if tech_score is not None:
                    if tech_score < -4:  # 非常负面的技术指标
                        cooling_factor += 1  # 延长冷却期
                    elif tech_score > 4:  # 非常正面的技术指标
                        cooling_factor = max(1, cooling_factor - 1)  # 缩短冷却期但至少为1

                # 计算基础冷却期长度
                base_cooling = int(consecutive_loss_count * cooling_factor)

                # 设置冷却期，添加随机波动避免固定周期交易
                # 使用已设置种子的随机数生成器确保结果可重复
                random_factor = random.uniform(0.8, 1.2)  # ±20%随机波动
                cooling_period = current_index + max(1, int(base_cooling * random_factor))

                # 如果连续亏损次数远超阈值，考虑延长冷却期
                if consecutive_loss_count >= adjusted_max_loss * 1.5:
                    cooling_period += int(consecutive_loss_count)  # 额外延长冷却期
        else:
            # 盈利交易后的调整：根据盈利幅度和市场状态动态调整连续亏损计数
            # 小幅盈利 (低于1%)
            if profit_pct < 0.01:
                consecutive_loss_count = max(0, consecutive_loss_count - 0.5)
            # 中等盈利 (1%-2%)
            elif profit_pct < 0.02:
                consecutive_loss_count = max(0, consecutive_loss_count - 1.0)
            # 较大盈利 (2%-3%)
            elif profit_pct < 0.03:
                consecutive_loss_count = max(0, consecutive_loss_count - 1.5)
            # 优异盈利 (大于3%)
            else:
                consecutive_loss_count = 0  # 优异盈利完全重置连续亏损计数

        return consecutive_loss_count, cooling_period

    @staticmethod
    def get_thresholds_by_market_state(market_state):
        """
        根据市场状态获取阈值参数 - 全面优化的针对性参数

        Parameters
        ----------
        market_state : str
            市场状态："bullish", "bearish", "neutral"

        Returns
        -------
        dict
            包含各种阈值的字典
        """
        result = {
            "pred_threshold": 0,  # 不再使用
            "tech_threshold": 3,  # 默认技术得分阈值
            "sell_pred_threshold": 0,  # 不再使用
            "tech_sell_threshold": -3,  # 默认技术得分卖出阈值
            "stop_loss_mult": 1.0,  # 止损倍数(相对于DEFAULT_STOP_LOSS_PCT)
            "take_profit_mult": 1.0,  # 止盈倍数(相对于DEFAULT_TAKE_PROFIT_PCT)
            "trailing_stop_mult": 1.0,  # 跟踪止损倍数(相对于DEFAULT_TRAILING_STOP_PCT)
            "position_pct": 1.0,  # 仓位百分比
            "signal_threshold_mult": 1.0  # 信号阈值倍数(相对于DEFAULT_SIGNAL_THRESHOLD)
        }

        # 根据市场状态调整阈值
        if market_state == "bullish":
            # 牛市策略：更激进的买入，更保守的卖出，提高获利目标
            result["tech_threshold"] = 3  # 降低买入门槛，更积极抓住牛市机会
            result["tech_sell_threshold"] = -7  # 大幅提高卖出门槛，让利润跑得更远
            result["stop_loss_mult"] = 1.3  # 放宽止损，避免小回调就出局
            result["take_profit_mult"] = 1.8  # 大幅提高止盈目标，充分利用牛市
            result["trailing_stop_mult"] = 0.9  # 跟踪止损稍紧，但仍较宽松，给价格更多上涨空间
            result["position_pct"] = 1.0  # 满仓
            result["signal_threshold_mult"] = 0.8  # 降低信号阈值，更容易进场
        elif market_state == "bearish":
            # 熊市策略：更保守的买入，更激进的卖出，降低风险
            result["tech_threshold"] = 6  # 保持高买入门槛，只买入非常强的信号
            result["tech_sell_threshold"] = -2  # 维持较低卖出门槛，更快止损出局
            result["stop_loss_mult"] = 0.7  # 收紧止损，减少亏损
            result["take_profit_mult"] = 0.7  # 降低止盈目标，有利润就跑
            result["trailing_stop_mult"] = 0.5  # 大幅收紧跟踪止损，保护资金
            result["position_pct"] = 0.6  # 进一步减仓至6成
            result["signal_threshold_mult"] = 1.4  # 提高信号阈值，更谨慎入场
        else:  # neutral
            # 中性市场策略：平衡买卖，适中风险
            result["tech_threshold"] = 4  # 适中的买入门槛
            result["tech_sell_threshold"] = -5  # 较高的卖出门槛，避免过早卖出
            result["stop_loss_mult"] = 1.0  # 标准止损
            result["take_profit_mult"] = 1.2  # 适当提高止盈目标
            result["trailing_stop_mult"] = 0.8  # 略微收紧跟踪止损
            result["position_pct"] = 0.9  # 轻微减仓到9成
            result["signal_threshold_mult"] = 1.0  # 标准信号阈值

        return result

    @staticmethod
    def confirm_signal(recent_signals, current_signal, signal_confirmation_window=None, signal_threshold=None,
                      tech_score=None, current_rsi=None, price_trend_down=None, current_pred_score=None, is_buy_signal=None):
        """
        确认信号 - 优化版本，更灵活的信号确认机制

        Parameters
        ----------
        recent_signals : list
            最近的信号列表
        current_signal : bool
            当前信号
        signal_confirmation_window : int, optional
            信号确认窗口, by default None (使用全局变量)
        signal_threshold : int, optional
            信号确认阈值, by default None (使用全局变量)
        tech_score : int, optional
            技术指标综合得分, by default None (用于强信号判断)
        current_rsi : float, optional
            RSI指标, by default None (用于强信号判断)
        price_trend_down : bool, optional
            价格趋势是否向下, by default None (用于强信号判断)
        current_pred_score : float, optional
            当前预测分数, by default None (用于记录信号生成时的预测分数)
        is_buy_signal : bool, optional
            是否为买入信号, by default None (用于区分买入和卖出强信号)

        Returns
        -------
        tuple
            如果提供了current_pred_score，返回(confirmed_signal, updated_recent_signals, signal_count, signal_pred_score)
            否则返回(confirmed_signal, updated_recent_signals, signal_count)
        """
        # 如果未指定参数，使用全局变量
        if signal_confirmation_window is None:
            signal_confirmation_window = DEFAULT_SIGNAL_CONFIRMATION_WINDOW
        if signal_threshold is None:
            signal_threshold = DEFAULT_SIGNAL_THRESHOLD

        # 加入强信号立即确认机制，基于技术指标和市场条件
        strong_signal = False
        if tech_score is not None and current_rsi is not None:
            # 买入强信号：更精细的条件判断
            if is_buy_signal:
                # 技术得分极高 - 强烈买入信号
                if tech_score >= 6:
                    strong_signal = True
                # 技术得分高 + RSI适中 - 较强买入信号
                elif tech_score >= 4 and 30 <= current_rsi <= 60:
                    strong_signal = True
                # RSI严重超卖 - 反弹买入信号
                elif current_rsi <= 30:
                    strong_signal = True
                # 技术得分中等 + 非下跌趋势 + RSI适中 - 中等买入信号
                elif tech_score >= 3 and price_trend_down is False and 35 <= current_rsi <= 65:
                    strong_signal = True

            # 卖出强信号：更精细的条件判断
            elif not is_buy_signal:
                # 技术得分极低 - 强烈卖出信号
                if tech_score <= -5:
                    strong_signal = True
                # 技术得分低 + RSI高 - 较强卖出信号
                elif tech_score <= -3 and current_rsi >= 65:
                    strong_signal = True
                # RSI严重超买 - 回调卖出信号
                elif current_rsi >= 75:
                    strong_signal = True
                # 下跌趋势 + 技术得分为负 + RSI高 - 中等卖出信号
                elif price_trend_down and tech_score < 0 and current_rsi >= 60:
                    strong_signal = True

        # 加权信号确认：最近的信号权重更高
        # 如果提供了预测分数，将信号和预测分数一起存储
        if current_pred_score is not None:
            # 更新信号历史，存储(信号, 预测分数)元组
            recent_signals.append((current_signal, current_pred_score))
            if len(recent_signals) > signal_confirmation_window:
                recent_signals.pop(0)

            # 加权信号计数：最近的信号权重更高
            weights = np.linspace(0.6, 1.0, len(recent_signals))  # 权重从0.6到1.0线性增加
            weighted_signal_count = sum(weights[i] * signal for i, (signal, _) in enumerate(recent_signals))

            # 计算加权阈值：窗口越大，阈值越高
            weighted_threshold = signal_threshold * (len(recent_signals) / signal_confirmation_window)

            # 信号确认：加权信号计数超过阈值或者有强信号
            confirmed_signal = (weighted_signal_count >= weighted_threshold) or strong_signal

            # 计算常规信号计数（用于日志输出）
            signal_count = sum(1 for signal, _ in recent_signals if signal)

            # 获取信号确认时的预测分数（使用最后一个确认的信号的预测分数）
            signal_pred_score = None
            if confirmed_signal:
                # 找到最近的确认信号的预测分数
                for signal, pred_score in reversed(recent_signals):
                    if signal:
                        signal_pred_score = pred_score
                        break

            # 不输出信号确认信息

            return confirmed_signal, recent_signals, signal_count, signal_pred_score
        else:
            # 原始逻辑，不包含预测分数
            # 更新信号历史
            recent_signals.append(current_signal)
            if len(recent_signals) > signal_confirmation_window:
                recent_signals.pop(0)

            # 加权信号计数：最近的信号权重更高
            weights = np.linspace(0.6, 1.0, len(recent_signals))  # 权重从0.6到1.0线性增加
            weighted_signal_count = sum(weights[i] * signal for i, signal in enumerate(recent_signals))

            # 计算加权阈值：窗口越大，阈值越高
            weighted_threshold = signal_threshold * (len(recent_signals) / signal_confirmation_window)

            # 信号确认：加权信号计数超过阈值或者有强信号
            confirmed_signal = (weighted_signal_count >= weighted_threshold) or strong_signal

            # 计算常规信号计数（用于日志输出）
            signal_count = sum(1 for signal in recent_signals if signal)

            # 不输出信号确认信息

            return confirmed_signal, recent_signals, signal_count

    @staticmethod
    def process_price_data(price_data, all_times, instrument):
        """
        处理价格数据，转换为更方便的格式

        Parameters
        ----------
        price_data : pd.DataFrame
            原始价格数据
        all_times : list
            所有时间点列表
        instrument : str
            股票代码

        Returns
        -------
        pd.DataFrame
            处理后的价格数据
        """
        price_df = pd.DataFrame(index=all_times)
        for time in all_times:
            try:
                price_df.loc[time, "close"] = price_data.loc[(instrument, time), "$close"]
                price_df.loc[time, "open"] = price_data.loc[(instrument, time), "$open"]
                price_df.loc[time, "high"] = price_data.loc[(instrument, time), "$high"]
                price_df.loc[time, "low"] = price_data.loc[(instrument, time), "$low"]
                price_df.loc[time, "volume"] = price_data.loc[(instrument, time), "$volume"]
            except Exception as e:
                logger.warning(f"获取{time}的价格数据失败: {e}")
                # 如果是第一个时间点且数据缺失，则跳过该时间点
                if time == all_times[0]:
                    logger.warning(f"第一个时间点 {time} 数据缺失，跳过该时间点")
                    continue
                # 否则使用前一个时间点的数据
                prev_time = all_times[all_times.index(time) - 1]
                price_df.loc[time] = price_df.loc[prev_time]
                logger.warning(f"使用前一个时间点的数据作为 {time} 的数据")
        return price_df



    @staticmethod
    def load_alpha158_features(price_df, instrument):
        """
        使用qlib构建Alpha158特征数据

        Parameters
        ----------
        price_df : pd.DataFrame
            价格数据，包含open, high, low, close, volume
        instrument : str
            股票代码

        Returns
        -------
        pd.DataFrame
            Alpha158特征数据
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 直接使用qlib，不再检查初始化状态

            # 使用qlib构建Alpha158特征
            logger.info(f"使用qlib构建Alpha158特征数据: {instrument}")

            # 导入qlib相关模块
            from qlib.contrib.data.handler import Alpha158

            # 准备qlib所需的数据格式
            # 将price_df转换为qlib所需的格式
            qlib_data = price_df.copy()
            qlib_data.columns = [f"${col}" for col in qlib_data.columns]  # qlib格式要求列名前加$
            qlib_data["instrument"] = instrument
            qlib_data = qlib_data.reset_index()
            qlib_data = qlib_data.rename(columns={"index": "datetime"})

            # 设置Alpha158处理器 - 优化版本
            try:
                # 使用特定股票代码而不是all
                try:
                    # 使用特定股票代码作为instruments
                    alpha158_handler = Alpha158(
                        instruments=[instrument],  # 使用具体的股票代码列表，而不是"all"
                        start_time=qlib_data["datetime"].min(),  # 使用数据中的最早时间
                        end_time=qlib_data["datetime"].max(),    # 使用数据中的最晚时间
                        freq="5min",  # 指定频率为5分钟
                        infer_processors=[],
                        learn_processors=[],
                    )
                    logger.info(f"成功使用股票代码 '{instrument}' 创建Alpha158处理器")
                except Exception as e1:
                    # 如果使用特定股票代码失败，直接报错，不尝试其他备选方案
                    error_msg = f"使用股票代码 '{instrument}' 创建Alpha158处理器失败: {e1}"
                    logger.error(error_msg)
                    import traceback
                    logger.error(traceback.format_exc())
                    raise ValueError(error_msg)

                # 如果成功创建了Alpha158处理器，继续执行

                logger.info("成功创建Alpha158处理器")

                # 创建一个临时的数据加载器
                from qlib.data.dataset.loader import DataLoader

                class CustomDataLoader(DataLoader):
                    def __init__(self, data):
                        self.data = data
                        # 使用Alpha158的标准特征和标签配置
                        # 使用自定义配置，确保包含所有需要的特征
                        from qlib.contrib.data.loader import Alpha158DL

                        # 创建一个包含所有特征的配置
                        rolling_config = {
                            "kbar": {},
                            "price": {
                                "windows": [0],
                                "feature": ["OPEN", "HIGH", "LOW", "CLOSE", "VWAP"],
                            },
                            "rolling": {
                                "windows": [5, 10, 20, 30, 60],
                                "include": ["ROC", "MA", "STD", "BETA", "RSQR", "RESI", "MAX", "LOW",
                                           "QTLU", "QTLD", "RANK", "RSV", "IMAX", "IMIN", "IMXD",
                                           "CORR", "CORD", "CNTP", "CNTN", "CNTD", "SUMP", "SUMN",
                                           "SUMD", "VMA", "VSTD", "WVMA", "VSUMP", "VSUMN", "VSUMD"],
                            }
                        }

                        # 使用Alpha158DL.get_feature_config获取特征配置
                        feature_fields, feature_names = Alpha158DL.get_feature_config(rolling_config)

                        self.config = {
                            "feature": (feature_fields, feature_names),
                            "label": Alpha158.get_label_config(Alpha158)
                        }

                    def load(self, **_):
                        # 忽略所有参数，直接返回预先准备好的数据
                        return self.data

                    def get_df_features(self):
                        # 返回特征配置
                        return self.config["feature"]

                    def get_df_labels(self):
                        # 返回标签配置
                        return self.config["label"]

                # 使用自定义数据加载器
                alpha158_handler.data_loader = CustomDataLoader(qlib_data)
                logger.info("成功设置自定义数据加载器")

            except Exception as e:
                logger.error(f"创建Alpha158处理器失败: {e}")
                import traceback
                logger.error(traceback.format_exc())
                raise

            # 使用Alpha158处理器计算特征 - 优化版本
            try:
                logger.info("开始计算Alpha158特征")
                # 使用特定股票代码进行特征获取 - 修复fetch调用方式
                # 使用slice(None)选择所有数据，level="datetime"指定按时间索引选择
                alpha158_features = alpha158_handler.fetch(slice(None), level="datetime")
                logger.info(f"成功获取股票 {instrument} 的Alpha158特征")
                logger.info("成功计算Alpha158特征")

                # 检查特征数据是否有效
                if alpha158_features is None or alpha158_features.empty:
                    logger.error("Alpha158特征计算结果为空")
                    raise ValueError("Alpha158特征计算结果为空")

                # 将特征数据转换为DataFrame
                logger.info("开始转换Alpha158特征为DataFrame")

                # 检查alpha158_features的类型和结构
                if isinstance(alpha158_features, pd.DataFrame):
                    # 如果已经是DataFrame，直接使用
                    feature_df = alpha158_features

                    # 打印索引信息以便调试
                    logger.info(f"Alpha158特征DataFrame索引类型: {type(feature_df.index)}")
                    logger.info(f"Alpha158特征DataFrame索引名称: {feature_df.index.names}")

                    # 处理索引
                    if isinstance(feature_df.index, pd.MultiIndex):
                        # 检查索引级别中是否有datetime
                        if 'datetime' in feature_df.index.names:
                            # 获取datetime值
                            # 提取datetime值作为新索引
                            datetimes = feature_df.index.get_level_values('datetime')
                            # 重置所有索引
                            feature_df = feature_df.reset_index()
                            # 设置datetime为索引
                            feature_df = feature_df.set_index(datetimes)
                            feature_df.index.name = 'datetime'
                        else:
                            # 如果没有datetime级别，但有时间类型的索引，使用第一个级别作为datetime
                            logger.info(f"索引级别中没有'datetime'，尝试使用第一个级别")
                            # 重置所有索引
                            feature_df = feature_df.reset_index()
                            # 使用第一列作为索引（通常是时间戳）
                            if len(feature_df.columns) > 0 and feature_df.columns[0] in ['datetime', 'date', 'time']:
                                feature_df = feature_df.set_index(feature_df.columns[0])
                            else:
                                # 如果没有合适的列，创建一个新的datetime索引
                                feature_df.index = pd.date_range(
                                    start=qlib_data["datetime"].min(),
                                    periods=len(feature_df),
                                    freq='5min'
                                )
                                feature_df.index.name = 'datetime'
                    else:
                        # 如果是普通索引，确保索引名称为datetime
                        if feature_df.index.name != 'datetime':
                            # 如果索引不是datetime，尝试使用现有索引作为datetime
                            logger.info(f"普通索引，索引名称: {feature_df.index.name}")
                            # 保存原始索引
                            orig_index = feature_df.index
                            # 重置索引
                            feature_df = feature_df.reset_index()
                            # 如果原始索引看起来像时间戳，使用它作为新索引
                            if isinstance(orig_index[0], (pd.Timestamp, datetime.datetime, str)):
                                feature_df = feature_df.set_index(feature_df.columns[0])
                                feature_df.index.name = 'datetime'
                            else:
                                # 否则创建新的datetime索引
                                feature_df.index = pd.date_range(
                                    start=qlib_data["datetime"].min(),
                                    periods=len(feature_df),
                                    freq='5min'
                                )
                                feature_df.index.name = 'datetime'
                else:
                    # 如果不是DataFrame，尝试转换
                    feature_items = []
                    for feature_name, feature_series in alpha158_features.data.items():
                        for idx, value in feature_series.items():
                            feature_items.append({
                                "datetime": idx[1] if isinstance(idx, tuple) else idx,  # 时间戳可能在索引的第二个位置
                                "feature_name": feature_name,
                                "feature_value": value
                            })

                    # 创建DataFrame并透视
                    feature_df = pd.DataFrame(feature_items)
                    feature_df = feature_df.pivot(index="datetime", columns="feature_name", values="feature_value")

                logger.info(f"成功转换Alpha158特征为DataFrame，形状: {feature_df.shape}")

            except Exception as e:
                logger.error(f"处理Alpha158特征数据失败: {e}")
                import traceback
                logger.error(traceback.format_exc())
                raise

            logger.info(f"成功构建Alpha158特征数据，形状: {feature_df.shape}")
            return feature_df

        except Exception as e:
            logger.error(f"构建Alpha158特征数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 直接抛出异常，不使用替代方案
            raise ValueError(f"构建Alpha158特征数据失败，无法继续执行: {e}")

    @staticmethod
    def calculate_technical_indicators(price_df, alpha158_df):
        """
        使用Alpha158特征

        Parameters
        ----------
        price_df : pd.DataFrame
            价格数据
        alpha158_df : pd.DataFrame
            Alpha158特征数据

        Returns
        -------
        pd.DataFrame
            添加Alpha158特征后的价格数据
        """
        import logging
        logger = logging.getLogger(__name__)

        # 确保索引匹配
        common_index = price_df.index.intersection(alpha158_df.index)
        
        # 检查是否有重复索引
        if price_df.index.duplicated().any():
            logger.warning("价格数据中发现重复索引，正在去除...")
            price_df = price_df[~price_df.index.duplicated(keep='first')]
        
        if alpha158_df.index.duplicated().any():
            logger.warning("Alpha158数据中发现重复索引，正在去除...")
            alpha158_df = alpha158_df[~alpha158_df.index.duplicated(keep='first')]
        
        # 重新计算共同索引
        common_index = price_df.index.intersection(alpha158_df.index)
        
        if len(common_index) == 0:
            logger.error("价格数据和Alpha158数据没有共同的时间索引")
            raise ValueError("价格数据和Alpha158数据没有共同的时间索引")
        
        # 根据共同索引对齐数据
        price_df = price_df.loc[common_index].copy()  # 使用copy()避免SettingWithCopyWarning
        alpha158_df = alpha158_df.loc[common_index].copy()
        
        logger.info(f"对齐后的数据形状 - 价格: {price_df.shape}, Alpha158: {alpha158_df.shape}")

        # 定义特征映射，将我们需要的特征名称映射到Alpha158特征名称
        # 这样即使Alpha158特征名称发生变化，我们也可以灵活处理
        price_df = price_df.loc[common_index]
        alpha158_df = alpha158_df.loc[common_index]

        # 定义特征映射，将我们需要的特征名称映射到Alpha158特征名称
        # 这样即使Alpha158特征名称发生变化，我们也可以灵活处理
        feature_mappings = {
            # 移动平均线相关特征
            'ma5': ('MA5', 'Mean($close, 5)/$close', 'Mean(close, 5)/close', 'mean_5'),
            'ma10': ('MA10', 'Mean($close, 10)/$close', 'Mean(close, 10)/close', 'mean_10'),
            'ma20': ('MA20', 'Mean($close, 20)/$close', 'Mean(close, 20)/close', 'mean_20'),

            # RSI相关特征
            'rsi': ('SUMP10', 'Sum(Greater($close-Ref($close, 1), 0), 10)/(Sum(Abs($close-Ref($close, 1)), 10)+1e-12)', 'sump_10'),

            # MACD相关特征
            'hist': ('SUMD10', '(Sum(Greater($close-Ref($close, 1), 0), 10)-Sum(Greater(Ref($close, 1)-$close, 0), 10))/(Sum(Abs($close-Ref($close, 1)), 10)+1e-12)', 'sumd_10'),

            # 波动率相关特征
            'volatility': ('STD20', 'Std($close, 20)/$close', 'Std(close, 20)/close', 'std_20'),

            # 其他有用的Alpha158特征
            'beta5': ('BETA5', 'Slope($close, 5)/$close', 'Slope(close, 5)/close', 'beta_5'),
            'beta10': ('BETA10', 'Slope($close, 10)/$close', 'Slope(close, 10)/close', 'beta_10'),
            'beta20': ('BETA20', 'Slope($close, 20)/$close', 'Slope(close, 20)/close', 'beta_20'),

            'rsqr10': ('RSQR10', 'Rsquare($close, 10)', 'Rsquare(close, 10)', 'rsquare_10'),
            'resi10': ('RESI10', 'Resi($close, 10)/$close', 'Resi(close, 10)/close', 'resi_10'),

            'cntp10': ('CNTP10', 'Mean($close>Ref($close, 1), 10)', 'Mean(close>Ref(close, 1), 10)', 'cntp_10'),
            'cntn10': ('CNTN10', 'Mean($close<Ref($close, 1), 10)', 'Mean(close<Ref(close, 1), 10)', 'cntn_10'),
            'cntd10': ('CNTD10', 'Mean($close>Ref($close, 1), 10)-Mean($close<Ref($close, 1), 10)', 'cntd_10'),

            'sump5': ('SUMP5', 'Sum(Greater($close-Ref($close, 1), 0), 5)/(Sum(Abs($close-Ref($close, 1)), 5)+1e-12)', 'sump_5'),
            'sump10': ('SUMP10', 'Sum(Greater($close-Ref($close, 1), 0), 10)/(Sum(Abs($close-Ref($close, 1)), 10)+1e-12)', 'sump_10'),
            'sump20': ('SUMP20', 'Sum(Greater($close-Ref($close, 1), 0), 20)/(Sum(Abs($close-Ref($close, 1)), 20)+1e-12)', 'sump_20'),

            'sumd5': ('SUMD5', '(Sum(Greater($close-Ref($close, 1), 0), 5)-Sum(Greater(Ref($close, 1)-$close, 0), 5))/(Sum(Abs($close-Ref($close, 1)), 5)+1e-12)', 'sumd_5'),
            'sumd10': ('SUMD10', '(Sum(Greater($close-Ref($close, 1), 0), 10)-Sum(Greater(Ref($close, 1)-$close, 0), 10))/(Sum(Abs($close-Ref($close, 1)), 10)+1e-12)', 'sumd_10'),
            'sumd20': ('SUMD20', '(Sum(Greater($close-Ref($close, 1), 0), 20)-Sum(Greater(Ref($close, 1)-$close, 0), 20))/(Sum(Abs($close-Ref($close, 1)), 20)+1e-12)', 'sumd_20'),

            'std5': ('STD5', 'Std($close, 5)/$close', 'Std(close, 5)/close', 'std_5'),
            'std10': ('STD10', 'Std($close, 10)/$close', 'Std(close, 10)/close', 'std_10'),
            'std20': ('STD20', 'Std($close, 20)/$close', 'Std(close, 20)/close', 'std_20'),
        }

        # 遍历特征映射，尝试添加每个特征
        for target_name, source_names in feature_mappings.items():
            try:
                # 尝试所有可能的源名称
                found = False
                for source_name in source_names:
                    if source_name in alpha158_df.columns:
                        # 如果是移动平均线相关特征，需要乘以价格
                        if target_name.startswith('ma'):
                            price_df[target_name] = alpha158_df[source_name] * price_df['close']
                        # 如果是RSI相关特征，需要乘以100
                        elif target_name == 'rsi':
                            price_df[target_name] = alpha158_df[source_name] * 100
                        # 其他特征直接赋值
                        else:
                            price_df[target_name] = alpha158_df[source_name]
                        found = True
                        break

                # 如果所有源名称都不存在，直接报错
                if not found:
                    # 对于关键特征，如果缺失，直接报错
                    if target_name in ['ma5', 'ma10', 'ma20', 'rsi']:
                        error_msg = f"无法找到特征 {target_name} 对应的Alpha158特征，请检查Alpha158特征数据"
                        logger.error(error_msg)
                        raise ValueError(error_msg)

            except Exception as e:
                error_msg = f"处理特征 {target_name} 时出错: {e}"
                logger.error(error_msg)
                # 对于关键特征，如果处理出错，直接报错
                if target_name in ['ma5', 'ma10', 'ma20', 'rsi']:
                    import traceback
                    logger.error(traceback.format_exc())
                    raise ValueError(f"处理关键特征 {target_name} 时出错，无法继续执行: {e}")

        return price_df



    @staticmethod
    def get_pred_score_for_time(pred_df, current_time, benchmark_id):
        """
        获取指定时间点的预测分数

        Parameters
        ----------
        pred_df : pd.DataFrame
            预测数据
        current_time : pd.Timestamp
            当前时间点
        benchmark_id : str
            股票代码

        Returns
        -------
        float
            预测分数
        """
        try:
            # 尝试直接获取预测分数
            # 先找到当前时间对应的所有行
            time_rows = pred_df.index.get_level_values(0) == current_time
            # 再找到对应股票代码的行
            instrument_rows = pred_df.index.get_level_values(-1) == benchmark_id
            # 组合条件
            matching_rows = time_rows & instrument_rows
            if matching_rows.any():
                current_pred = pred_df.loc[matching_rows].iloc[0]
            else:
                raise KeyError("找不到匹配的预测数据")
            if isinstance(current_pred, pd.Series):
                current_pred_score = current_pred.iloc[0]
            else:
                current_pred_score = current_pred
        except:
            try:
                # 尝试获取最接近的时间点的预测分数
                pred_times = pred_df.index.get_level_values(0)
                closest_time = pred_times[pred_times <= current_time].max()
                if pd.isna(closest_time):
                    return 0
                else:
                    # 找到最接近时间对应的所有行
                    time_rows = pred_df.index.get_level_values(0) == closest_time
                    # 再找到对应股票代码的行
                    instrument_rows = pred_df.index.get_level_values(-1) == benchmark_id
                    # 组合条件
                    matching_rows = time_rows & instrument_rows
                    if matching_rows.any():
                        current_pred = pred_df.loc[matching_rows].iloc[0]
                    else:
                        raise KeyError("找不到匹配的预测数据")
                    if isinstance(current_pred, pd.Series):
                        current_pred_score = current_pred.iloc[0]
                    else:
                        current_pred_score = current_pred
            except:
                # 如果无法获取预测分数，设置为0
                return 0

        return current_pred_score

    @staticmethod
    def should_buy(buy_signal, shares, last_trade_time, current_time, min_trade_interval):
        """
        判断是否应该买入

        Parameters
        ----------
        buy_signal : bool
            买入信号
        shares : int
            当前持有股数
        last_trade_time : pd.Timestamp
            上次交易时间
        current_time : pd.Timestamp
            当前时间
        min_trade_interval : int
            最小交易间隔（分钟）

        Returns
        -------
        bool
            是否应该买入
        """
        # 没有买入信号，不买入
        if not buy_signal:
            return False

        # 如果已有持仓，不买入
        if shares > 0:
            return False

        # 如果没有交易过，可以买入
        if last_trade_time is None:
            return True

        # 计算距离上次交易的时间间隔（分钟）
        time_since_last_trade = (current_time - last_trade_time).total_seconds() / 60

        # 如果时间间隔大于最小交易间隔，可以买入
        if time_since_last_trade >= min_trade_interval:
            return True

        return False

    @staticmethod
    def should_sell(sell_signal, stop_loss_signal, take_profit_signal, trailing_stop_signal, shares):
        """
        判断是否应该卖出

        Parameters
        ----------
        sell_signal : bool
            卖出信号
        stop_loss_signal : bool
            止损信号
        take_profit_signal : bool
            止盈信号
        trailing_stop_signal : bool
            跟踪止损信号
        shares : int
            当前持有股数

        Returns
        -------
        bool
            是否应该卖出
        """
        return ((sell_signal or
                stop_loss_signal or
                take_profit_signal or
                trailing_stop_signal) and
                shares > 0)

    @staticmethod
    def get_sell_reason(stop_loss_signal, take_profit_signal, trailing_stop_signal):
        """
        获取卖出原因

        Parameters
        ----------
        stop_loss_signal : bool
            止损信号
        take_profit_signal : bool
            止盈信号
        trailing_stop_signal : bool
            跟踪止损信号

        Returns
        -------
        str
            卖出原因
        """
        sell_reason = "常规卖出"
        if stop_loss_signal:
            sell_reason = "止损"
        elif take_profit_signal:
            sell_reason = "止盈"
        elif trailing_stop_signal:
            sell_reason = "跟踪止损"
        return sell_reason

    @staticmethod
    def calculate_backtest_metrics(portfolio_df, price_df):
        """
        计算回测指标

        Parameters
        ----------
        portfolio_df : pd.DataFrame
            投资组合数据
        price_df : pd.DataFrame
            价格数据

        Returns
        -------
        dict
            回测指标
        """
        # 计算收益率和其他指标
        portfolio_df["return"] = portfolio_df["portfolio_value"].pct_change()
        portfolio_df["cum_return"] = (1 + portfolio_df["return"]).cumprod() - 1

        # 计算回测指标
        returns = portfolio_df["return"].dropna()
        cum_returns = portfolio_df["cum_return"].dropna()

        # 计算基准收益率
        benchmark_returns = price_df["close"].pct_change()
        benchmark_cum_returns = (1 + benchmark_returns).cumprod() - 1

        # 处理收益率数据，去除异常值
        returns_cleaned = returns.replace([np.inf, -np.inf], np.nan).dropna()

        # 计算年化收益率
        annual_return = (1 + cum_returns.iloc[-1]) ** (252 / len(returns_cleaned) * 48) - 1 if not returns_cleaned.empty else 0

        # 计算年化波动率
        annual_vol = returns_cleaned.std() * np.sqrt(252 * 48) if not returns_cleaned.empty else 0

        # 限制波动率的合理范围
        annual_vol = min(annual_vol, 1.0)  # 限制最大波动率为100%

        # 计算夏普比率
        risk_free_rate = 0.03  # 假设无风险利率为3%
        sharpe_ratio = (annual_return - risk_free_rate) / annual_vol if annual_vol != 0 else 0

        # 计算最大回撤
        portfolio_values = portfolio_df["portfolio_value"]
        portfolio_values_peak = portfolio_values.cummax()
        drawdown_direct = (portfolio_values - portfolio_values_peak) / portfolio_values_peak
        max_drawdown = drawdown_direct.min()  # 使用实际的最大回撤值

        # 打印回撤详情
        worst_drawdown_idx = drawdown_direct.idxmin()
        drawdown_duration = 0
        if worst_drawdown_idx is not None:
            worst_drawdown_time = worst_drawdown_idx
            peak_time = portfolio_values_peak.loc[:worst_drawdown_time].idxmax()

            # 计算最大回撤期间
            drawdown_duration = (worst_drawdown_time - peak_time).total_seconds() / 60 / 60 / 24  # 转换为天

        # 基准收益率的最大回撤计算
        benchmark_prices = price_df["close"]
        benchmark_prices_peak = benchmark_prices.cummax()
        benchmark_drawdown = (benchmark_prices - benchmark_prices_peak) / benchmark_prices_peak
        benchmark_max_drawdown = benchmark_drawdown.min()  # 使用实际的最大回撤值

        # 计算基准收益率
        benchmark_returns_cleaned = benchmark_returns.replace([np.inf, -np.inf], np.nan).dropna()
        benchmark_annual_return = (1 + benchmark_cum_returns.iloc[-1]) ** (252 / len(benchmark_returns_cleaned) * 48) - 1 if not benchmark_returns_cleaned.empty else 0
        benchmark_annual_vol = benchmark_returns_cleaned.std() * np.sqrt(252 * 48) if not benchmark_returns_cleaned.empty else 0
        benchmark_sharpe = (benchmark_annual_return - risk_free_rate) / benchmark_annual_vol if benchmark_annual_vol != 0 else 0

        metrics = {
            "累积收益率": cum_returns.iloc[-1],
            "年化收益率": annual_return,
            "年化波动率": annual_vol,
            "夏普比率": sharpe_ratio,
            "最大回撤": max_drawdown,
            "最大回撤持续天数": drawdown_duration,
            "基准累积收益率": benchmark_cum_returns.iloc[-1],
            "基准年化收益率": benchmark_annual_return,
            "基准年化波动率": benchmark_annual_vol,
            "基准夏普比率": benchmark_sharpe,
            "基准最大回撤": benchmark_max_drawdown
        }

        return metrics

    @staticmethod
    def init_trading_params(initial_capital=400000):
        """
        初始化交易参数

        Parameters
        ----------
        initial_capital : float, optional
            初始资金, by default 400000

        Returns
        -------
        dict
            包含初始化交易参数的字典
        """
        # 直接从trading_strategy模块导入全局变量
        from trading_strategy import DEFAULT_STOP_LOSS_PCT, DEFAULT_TAKE_PROFIT_PCT, DEFAULT_TRAILING_STOP_PCT
        from trading_strategy import DEFAULT_MIN_TRADE_INTERVAL, DEFAULT_SIGNAL_THRESHOLD, DEFAULT_SIGNAL_CONFIRMATION_WINDOW
        from trading_strategy import DEFAULT_MAX_CONSECUTIVE_LOSS, DEFAULT_TREND_WINDOW, DEFAULT_MARKET_STATE_WINDOW

        return {
            # 交易参数
            "last_trade_price": None,
            "last_trade_time": None,
            "stop_loss_pct": DEFAULT_STOP_LOSS_PCT,
            "take_profit_pct": DEFAULT_TAKE_PROFIT_PCT,
            "trailing_stop_pct": DEFAULT_TRAILING_STOP_PCT,
            "min_trade_interval": DEFAULT_MIN_TRADE_INTERVAL,
            "buy_signal_count": 0,
            "sell_signal_count": 0,
            "signal_threshold": DEFAULT_SIGNAL_THRESHOLD,
            "signal_confirmation_window": DEFAULT_SIGNAL_CONFIRMATION_WINDOW,
            "max_price_since_buy": 0,
            "consecutive_loss_count": 0,
            "max_consecutive_loss": DEFAULT_MAX_CONSECUTIVE_LOSS,
            "cooling_period": 0,

            # 价格趋势跟踪
            "price_trend": [],
            "trend_window": DEFAULT_TREND_WINDOW,

            # 信号记录
            "recent_buy_signals": [],
            "recent_sell_signals": [],
            "buy_signal_pred_score": None,
            "sell_signal_pred_score": None,

            # 市场状态跟踪
            "market_state": "neutral",
            "market_state_window": DEFAULT_MARKET_STATE_WINDOW,
            "market_state_history": [],

            # 投资组合状态
            "cash": initial_capital,
            "shares": 0,

            # 交易记录
            "trade_records": [],

            # 投资组合价值记录
            "portfolio_values": []
        }

    @staticmethod
    def execute_buy_order(params, current_time, current_price, current_pred_score, tech_score, tech_indicators):
        """
        执行买入订单

        Parameters
        ----------
        params : dict
            交易参数字典
        current_time : pd.Timestamp
            当前时间点
        current_price : float
            当前价格
        current_pred_score : float
            当前预测分数
        tech_score : int
            技术指标综合得分
        tech_indicators : dict
            技术指标数据

        Returns
        -------
        dict
            更新后的交易参数
        str
            交易决策
        str
            交易原因
        """
        # 不需要导入logging，因为没有使用logger

        # 计算可买入的股数（考虑手续费）- 始终使用满仓
        available_cash = params["cash"]
        max_shares = int(available_cash / (current_price * (1 + 0.0003)) / 100) * 100  # 取整百

        decision = "hold"
        reason = "资金不足，无法买入"

        if max_shares > 0:
            params["shares"] = max_shares
            cost = params["shares"] * current_price * (1 + 0.0003)  # 考虑手续费
            params["cash"] -= cost
            params["last_trade_price"] = current_price
            params["last_trade_time"] = current_time
            params["max_price_since_buy"] = current_price

            # 记录交易
            params["trade_records"].append({
                "datetime": current_time,
                "action": "买入",
                "price": current_price,
                "shares": params["shares"],
                "cost": cost,
                "cash_after": params["cash"],
                "position_type": "满仓",
                "pred_score": current_pred_score,
                "tech_score": tech_score,
                "ma5": tech_indicators["ma5"],
                "ma10": tech_indicators["ma10"],
                "rsi": tech_indicators["rsi"],
                "macd_hist": tech_indicators["hist"]
            })

            decision = "buy"
            reason = f"买入信号确认 (技术得分: {tech_score})"

        return params, decision, reason

    @staticmethod
    def execute_sell_order(params, current_time, current_price, current_pred_score, tech_score, tech_indicators,
                           sell_reason, stop_loss_signal=False, take_profit_signal=False, trailing_stop_signal=False):
        """
        执行卖出订单

        Parameters
        ----------
        params : dict
            交易参数字典
        current_time : pd.Timestamp
            当前时间点
        current_price : float
            当前价格
        current_pred_score : float
            当前预测分数
        tech_score : int
            技术指标综合得分
        tech_indicators : dict
            技术指标数据
        sell_reason : str
            卖出原因
        stop_loss_signal : bool, optional
            止损信号, by default False
        take_profit_signal : bool, optional
            止盈信号, by default False
        trailing_stop_signal : bool, optional
            跟踪止损信号, by default False

        Returns
        -------
        dict
            更新后的交易参数
        str
            交易决策
        str
            交易原因
        """
        # 不需要导入logging，因为没有使用logger

        # 如果没有提供卖出原因，根据信号确定
        if not sell_reason:
            sell_reason = TradingStrategy.get_sell_reason(stop_loss_signal, take_profit_signal, trailing_stop_signal)

        # 全部卖出
        sell_amount = params["shares"]
        revenue = sell_amount * current_price * (1 - 0.0003)  # 考虑手续费
        params["cash"] += revenue

        # 计算收益率
        profit_pct = 0
        if params["last_trade_price"] is not None:
            profit_pct = (current_price - params["last_trade_price"]) / params["last_trade_price"] * 100
            # 更新连续亏损计数和冷却期 - 传递市场状态和技术得分
            params["consecutive_loss_count"], new_cooling_period = TradingStrategy.update_cooling_period(
                profit_pct/100,  # 转换为小数形式
                params["consecutive_loss_count"],
                params.get("current_index", 0),
                params["max_consecutive_loss"],
                market_state=params.get("market_state", "neutral"),  # 传递市场状态
                tech_score=tech_score  # 传递技术得分
            )

            # 更新冷却期
            if new_cooling_period > 0:
                params["cooling_period"] = new_cooling_period

        # 记录交易
        params["trade_records"].append({
            "datetime": current_time,
            "action": sell_reason,
            "price": current_price,
            "shares": sell_amount,
            "revenue": revenue,
            "cash_after": params["cash"],
            "profit_pct": profit_pct,
            "pred_score": current_pred_score,
            "tech_score": tech_score,
            "ma5": tech_indicators["ma5"],
            "ma10": tech_indicators["ma10"],
            "rsi": tech_indicators["rsi"],
            "macd_hist": tech_indicators["hist"]
        })

        # 更新持仓数量
        params["shares"] = 0

        # 重置变量
        params["last_trade_time"] = current_time
        params["max_price_since_buy"] = 0

        decision = "sell"
        reason = f"{sell_reason} (技术得分: {tech_score})"

        return params, decision, reason

    @staticmethod
    def record_portfolio_value(params, current_time, current_price, current_pred_score):
        """
        记录投资组合价值

        Parameters
        ----------
        params : dict
            交易参数字典
        current_time : pd.Timestamp
            当前时间点
        current_price : float
            当前价格
        current_pred_score : float
            当前预测分数

        Returns
        -------
        dict
            更新后的交易参数
        """
        # 计算当前持仓价值
        position_value = params["shares"] * current_price

        # 计算当前投资组合价值
        current_portfolio_value = params["cash"] + position_value

        # 记录投资组合价值
        params["portfolio_values"].append({
            "datetime": current_time,
            "portfolio_value": current_portfolio_value,
            "cash": params["cash"],
            "shares": params["shares"],
            "close_price": current_price,
            "position_value": position_value,
            "pred_score": current_pred_score
        })

        return params

    @staticmethod
    def process_single_trading_step(params, i, current_time, pred_df, benchmark_id, price_df, alpha158_df):
        """
        处理单个交易步骤

        Parameters
        ----------
        params : dict
            交易参数字典
        i : int
            当前索引
        current_time : pd.Timestamp
            当前时间点
        pred_df : pd.DataFrame
            预测数据
        benchmark_id : str
            基准股票代码
        price_df : pd.DataFrame
            价格数据
        alpha158_df : pd.DataFrame
            Alpha158特征数据

        Returns
        -------
        dict
            更新后的交易参数
        dict
            包含当前步骤决策的字典
        """
        import logging
        logger = logging.getLogger(__name__)

        # 保存当前索引，用于冷却期计算
        params["current_index"] = i

        # 获取当前价格
        current_price = price_df.loc[current_time, "close"]

        # 获取当前技术指标
        tech_indicators = {
            "ma5": price_df.loc[current_time, "ma5"],
            "ma10": price_df.loc[current_time, "ma10"],
            "ma20": price_df.loc[current_time, "ma20"],
            "rsi": price_df.loc[current_time, "rsi"],
            "hist": price_df.loc[current_time, "hist"],
            "volatility": price_df.loc[current_time, "volatility"]
        }

        # 创建Alpha158特征字典
        alpha158_features = {}
        if current_time in alpha158_df.index:
            # 提取当前时间点的Alpha158特征
            current_alpha158 = alpha158_df.loc[current_time]



            # 创建特征名称映射字典，包含多种可能的特征名称格式
            feature_name_mappings = {
                'MA5': ['MA5', 'Mean($close, 5)/$close', 'Mean(close, 5)/close', 'mean_5'],
                'MA10': ['MA10', 'Mean($close, 10)/$close', 'Mean(close, 10)/close', 'mean_10'],
                'MA20': ['MA20', 'Mean($close, 20)/$close', 'Mean(close, 20)/close', 'mean_20'],
                'BETA5': ['BETA5', 'Slope($close, 5)/$close', 'Slope(close, 5)/close', 'beta_5'],
                'BETA10': ['BETA10', 'Slope($close, 10)/$close', 'Slope(close, 10)/close', 'beta_10'],
                'BETA20': ['BETA20', 'Slope($close, 20)/$close', 'Slope(close, 20)/close', 'beta_20'],
                'RSQR10': ['RSQR10', 'Rsquare($close, 10)', 'Rsquare(close, 10)', 'rsquare_10'],
                'RESI10': ['RESI10', 'Resi($close, 10)/$close', 'Resi(close, 10)/close', 'resi_10'],
                'CNTP10': ['CNTP10', 'Mean($close>Ref($close, 1), 10)', 'Mean(close>Ref(close, 1), 10)', 'cntp_10'],
                'CNTN10': ['CNTN10', 'Mean($close<Ref($close, 1), 10)', 'Mean(close<Ref(close, 1), 10)', 'cntn_10'],
                'CNTD10': ['CNTD10', 'Mean($close>Ref($close, 1), 10)-Mean($close<Ref($close, 1), 10)', 'cntd_10'],
                'SUMP5': ['SUMP5', 'Sum(Greater($close-Ref($close, 1), 0), 5)/(Sum(Abs($close-Ref($close, 1)), 5)+1e-12)', 'sump_5'],
                'SUMP10': ['SUMP10', 'Sum(Greater($close-Ref($close, 1), 0), 10)/(Sum(Abs($close-Ref($close, 1)), 10)+1e-12)', 'sump_10'],
                'SUMP20': ['SUMP20', 'Sum(Greater($close-Ref($close, 1), 0), 20)/(Sum(Abs($close-Ref($close, 1)), 20)+1e-12)', 'sump_20'],
                'SUMD5': ['SUMD5', '(Sum(Greater($close-Ref($close, 1), 0), 5)-Sum(Greater(Ref($close, 1)-$close, 0), 5))/(Sum(Abs($close-Ref($close, 1)), 5)+1e-12)', 'sumd_5'],
                'SUMD10': ['SUMD10', '(Sum(Greater($close-Ref($close, 1), 0), 10)-Sum(Greater(Ref($close, 1)-$close, 0), 10))/(Sum(Abs($close-Ref($close, 1)), 10)+1e-12)', 'sumd_10'],
                'SUMD20': ['SUMD20', '(Sum(Greater($close-Ref($close, 1), 0), 20)-Sum(Greater(Ref($close, 1)-$close, 0), 20))/(Sum(Abs($close-Ref($close, 1)), 20)+1e-12)', 'sumd_20'],
                'STD5': ['STD5', 'Std($close, 5)/$close', 'Std(close, 5)/close', 'std_5'],
                'STD10': ['STD10', 'Std($close, 10)/$close', 'Std(close, 10)/close', 'std_10'],
                'STD20': ['STD20', 'Std($close, 20)/$close', 'Std(close, 20)/close', 'std_20']
            }

            # 初始化Alpha158特征字典
            alpha158_features = {}

            # 遍历特征名称映射，尝试找到匹配的特征名称
            for feature_key, possible_names in feature_name_mappings.items():
                feature_value = None
                # 尝试所有可能的特征名称
                for name in possible_names:
                    # 检查特征名称是否在当前Alpha158特征中
                    if name in current_alpha158.index:
                        feature_value = current_alpha158[name]
                        break

                # 如果找不到匹配的特征名称，使用默认值
                if feature_value is None:
                    # 对于MA类特征，默认值为1
                    if feature_key in ['MA5', 'MA10', 'MA20']:
                        feature_value = 1
                    # 对于SUMP类特征，默认值为0.5
                    elif feature_key in ['SUMP5', 'SUMP10', 'SUMP20']:
                        feature_value = 0.5
                    # 对于其他特征，默认值为0
                    else:
                        feature_value = 0

                # 将特征添加到Alpha158特征字典中
                alpha158_features[feature_key] = feature_value

            # 检查是否所有特征都使用了默认值
            default_values_count = 0
            for key, value in alpha158_features.items():
                if key in ['MA5', 'MA10', 'MA20'] and value == 1:
                    default_values_count += 1
                elif key not in ['MA5', 'MA10', 'MA20'] and value == 0:
                    default_values_count += 1

        else:
            logger.warning(f"当前时间点 {current_time} 在Alpha158特征数据中不存在")
            return params, {"datetime": current_time, "decision": "hold", "reason": "Alpha158特征数据缺失"}

        # 获取当前预测分数
        current_pred_score = TradingStrategy.get_pred_score_for_time(pred_df, current_time, benchmark_id)

        # 更新最高价格（用于跟踪止损）
        if params["shares"] > 0 and current_price > params["max_price_since_buy"]:
            params["max_price_since_buy"] = current_price

        # 更新价格趋势
        if len(params["price_trend"]) >= params["trend_window"]:
            params["price_trend"].pop(0)
        params["price_trend"].append(current_price)

        # 计算价格趋势
        price_trend_up, price_trend_down, _, _, _ = TradingStrategy.calculate_price_trend(params["price_trend"])

        # 更新市场状态
        params["market_state"], params["market_state_history"] = TradingStrategy.update_market_state(
            params["price_trend"], params["market_state_history"], params["market_state"], params["market_state_window"]
        )

        # 使用Alpha158特征计算技术得分
        tech_score = TradingStrategy.calculate_tech_score(alpha158_features)

        # 获取基于市场状态的阈值
        thresholds = TradingStrategy.get_thresholds_by_market_state(params["market_state"])
        tech_threshold = thresholds["tech_threshold"]
        # tech_sell_threshold不再使用，因为卖出信号直接由generate_sell_signal生成

        # 应用市场状态相关的止损止盈设置
        stop_loss_pct = params["stop_loss_pct"] * thresholds["stop_loss_mult"]
        take_profit_pct = params["take_profit_pct"] * thresholds["take_profit_mult"]
        trailing_stop_pct = params["trailing_stop_pct"] * thresholds["trailing_stop_mult"]

        # 调整信号确认阈值
        signal_threshold = params["signal_threshold"] * thresholds["signal_threshold_mult"]

        # 生成买入信号 - 只使用技术得分
        current_buy_signal = TradingStrategy.generate_buy_signal(
            tech_score=tech_score, price_trend_up=price_trend_up,
            current_ma5=tech_indicators["ma5"], current_ma10=tech_indicators["ma10"], current_ma20=tech_indicators["ma20"],
            current_rsi=tech_indicators["rsi"], market_state=params["market_state"],
            tech_threshold=tech_threshold, current_price=current_price
        )

        # 确认买入信号 - 使用调整后的信号阈值
        # 忽略buy_signal_count，因为它只用于日志输出
        buy_signal, params["recent_buy_signals"], _, params["buy_signal_pred_score"] = TradingStrategy.confirm_signal(
            params["recent_buy_signals"], current_buy_signal, params["signal_confirmation_window"], signal_threshold,
            tech_score=tech_score, current_rsi=tech_indicators["rsi"], price_trend_down=price_trend_down,
            current_pred_score=current_pred_score, is_buy_signal=True
        )

        # 生成卖出信号 - 只使用技术得分和技术得分卖出阈值
        current_sell_signal = TradingStrategy.generate_sell_signal(
            tech_score, price_trend_down, tech_indicators["ma5"], tech_indicators["ma10"], tech_indicators["ma20"],
            tech_indicators["rsi"], params["market_state"], current_price
        )

        # 确认卖出信号 - 使用调整后的信号阈值
        # 忽略sell_signal_count，因为它只用于日志输出
        confirmed_sell_signal, params["recent_sell_signals"], _, params["sell_signal_pred_score"] = TradingStrategy.confirm_signal(
            params["recent_sell_signals"], current_sell_signal, params["signal_confirmation_window"], signal_threshold,
            tech_score=tech_score, current_rsi=tech_indicators["rsi"], price_trend_down=price_trend_down,
            current_pred_score=current_pred_score, is_buy_signal=False
        )

        # 只有在有持仓的情况下才生成卖出信号
        sell_signal = params["shares"] > 0 and confirmed_sell_signal

        # 生成止损、止盈和跟踪止损信号 - 使用调整后的止损/止盈参数
        if params["shares"] > 0:
            stop_loss_signal, take_profit_signal, trailing_stop_signal = TradingStrategy.generate_stop_signals(
                current_price, params["last_trade_price"], params["max_price_since_buy"],
                stop_loss_pct, take_profit_pct, trailing_stop_pct, params["market_state"]
            )
        else:
            stop_loss_signal, take_profit_signal, trailing_stop_signal = False, False, False

        # 默认交易决策
        decision = "hold"
        reason = "无交易信号"

        # 如果在冷却期内，不进行交易
        if i <= params["cooling_period"]:
            decision = "hold"
            reason = "冷却期内，暂停交易"
            logger.debug(f"在冷却期内，跳过交易 {current_time}")
        # 买入条件
        elif TradingStrategy.should_buy(buy_signal, params["shares"], params["last_trade_time"],
                                      current_time, params["min_trade_interval"]):
            params, decision, reason = TradingStrategy.execute_buy_order(
                params, current_time, current_price, current_pred_score, tech_score, tech_indicators
            )
        # 卖出条件
        elif TradingStrategy.should_sell(sell_signal, stop_loss_signal, take_profit_signal,
                                       trailing_stop_signal, params["shares"]):
            # 获取卖出原因
            sell_reason = TradingStrategy.get_sell_reason(stop_loss_signal, take_profit_signal, trailing_stop_signal)

            params, decision, reason = TradingStrategy.execute_sell_order(
                params, current_time, current_price, current_pred_score, tech_score, tech_indicators,
                sell_reason, stop_loss_signal, take_profit_signal, trailing_stop_signal
            )

        # 记录投资组合价值
        params = TradingStrategy.record_portfolio_value(params, current_time, current_price, current_pred_score)

        # 返回更新后的参数和当前步骤的决策
        step_result = {
            "datetime": current_time,
            "decision": decision,
            "reason": reason,
            "price": current_price,
            "pred_score": current_pred_score,
            "tech_score": tech_score,
            "market_state": params["market_state"],
            "portfolio_value": params["portfolio_values"][-1]["portfolio_value"],
            "cash": params["cash"],
            "shares": params["shares"]
        }

        return params, step_result

    @staticmethod
    def run_backtest(price_data, pred_df, benchmark_id, start_time, end_time, initial_cash=100000,
                    stop_loss_pct=0.05, take_profit_pct=0.15, trailing_stop_pct=0.08, min_trade_interval=12,
                    signal_threshold=3, signal_confirmation_window=5, cooling_period=20):
        """
        运行回测

        Parameters
        ----------
        price_data : pd.DataFrame
            价格数据
        pred_df : pd.DataFrame
            预测数据
        benchmark_id : str
            基准股票代码
        start_time : pd.Timestamp
            开始时间
        end_time : pd.Timestamp
            结束时间
        initial_cash : float, optional
            初始资金, by default 100000
        fee_rate : float, optional
            交易费率, by default 0.0003
        stop_loss_pct : float, optional
            止损百分比, by default 0.05
        take_profit_pct : float, optional
            止盈百分比, by default 0.15
        trailing_stop_pct : float, optional
            跟踪止损百分比, by default 0.08
        min_trade_interval : int, optional
            最小交易间隔, by default 12
        signal_threshold : int, optional
            信号阈值, by default 3
        signal_confirmation_window : int, optional
            信号确认窗口, by default 5
        cooling_period : int, optional
            冷却期, by default 20

        Returns
        -------
        dict
            回测结果
        """
        import logging
        logger = logging.getLogger(__name__)

        # 不再检查qlib初始化状态

        # 获取所有时间点
        all_times = sorted(set(idx[1] for idx in price_data.index if start_time <= idx[1] <= end_time))

        # 处理价格数据
        price_df = TradingStrategy.process_price_data(price_data, all_times, benchmark_id)

        # 使用qlib构建Alpha158特征数据
        alpha158_df = TradingStrategy.load_alpha158_features(price_df, benchmark_id)
        if alpha158_df is None:
            logger.error(f"无法构建Alpha158特征数据")
            return None

        # 使用Alpha158特征计算技术指标
        price_df = TradingStrategy.calculate_technical_indicators(price_df, alpha158_df)

        # 初始化交易参数
        params = TradingStrategy.init_trading_params(initial_cash)

        # 更新交易参数
        params["stop_loss_pct"] = stop_loss_pct
        params["take_profit_pct"] = take_profit_pct
        params["trailing_stop_pct"] = trailing_stop_pct
        params["min_trade_interval"] = min_trade_interval
        params["signal_threshold"] = signal_threshold
        params["signal_confirmation_window"] = signal_confirmation_window
        params["cooling_period"] = cooling_period

        # 存储每个步骤的结果
        step_results = []

        # 遍历每个时间点
        for i, current_time in enumerate(all_times):
            # 处理单个交易步骤
            params, step_result = TradingStrategy.process_single_trading_step(
                params, i, current_time, pred_df, benchmark_id, price_df, alpha158_df
            )
            step_results.append(step_result)

            # 不输出详细信息，已在买入/卖出执行时输出关键信息

        # 创建回测结果DataFrame
        portfolio_df = pd.DataFrame([step["portfolio_value"] for step in step_results], index=[step["datetime"] for step in step_results])
        portfolio_df.columns = ["portfolio_value"]

        # 计算回测指标
        metrics = TradingStrategy.calculate_backtest_metrics(portfolio_df, price_df)

        # 构建回测结果
        backtest_results = {
            "metrics": metrics,
            "portfolio_df": portfolio_df,
            "trade_records": params["trade_records"],
            "step_results": step_results
        }

        return backtest_results
